server:
  port: 9095

spring:
  application:
    name: ds-socket-login-system

  # Redis Configuration
  data:
    redis:
      host: *************
      port: 6380
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms
        shutdown-timeout: 100ms

  # Jackson Configuration
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  # Security Configuation
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri:  https://testid.shinhansec.com.vn/t/testopenapi.shinhansec.com.vn/oauth2/token

# Socket.IO Server Configuration
socketio:
  server:
    hostname: localhost
    port: 9094
    worker-threads: 100
    boss-threads: 1
    allow-custom-requests: true
    upgrade-timeout: 10000
    ping-timeout: 60000
    ping-interval: 25000
    max-frame-payload-length: 1048576
    max-http-content-length: 1048576
    cors:
      allowed-origins: "*"
      allowed-methods: "GET,POST"
      allowed-headers: "*"
      allow-credentials: true

# Node.js Server Configuration
nodejs:
  server:
#    url: "wss://test.shinhansec.com.vn"
    socket-url: "wss://test.shinhansec.com.vn"
    connection-timeout: 5000
    read-timeout: 10000
    write-timeout: 10000
    reconnect-attempts: 5
    reconnect-delay: 2000
    heartbeat-interval: 30000
  socket:
    namespace: "/services"
    reconnect-attempts: 5
    reconnect-delay: 1000
    heartbeat-interval: 30000
    connection-timeout: 5000

# Session Management
session:
  default-timeout: 1800 # 30 minutes in seconds
  cleanup-interval: 300 # 5 minutes in seconds
  max-sessions-per-user: 5 # Legacy config
  max-devices-per-channel: 3 # New: max devices per channel (WTS, MTS, HTS, MWTS)
  redis-key-prefix: "fos:"

## Monitoring Configuration
#management:
#  endpoints:
#    web:
#      exposure:
#        include: health,info,metrics,prometheus
#      base-path: /actuator
#  endpoint:
#    health:
#      show-details: always
#    metrics:
#      enabled: true
#  metrics:
#    export:
#      prometheus:
#        enabled: true
#    distribution:
#      percentiles-histogram:
#        http.server.requests: true
#      percentiles:
#        http.server.requests: 0.5, 0.95, 0.99
#      sla:
#        http.server.requests: 100ms, 500ms, 1s

# Logging Configuration
logging:
  level:
    com.example.dssocketlogin: INFO
    com.corundumstudio.socketio: INFO
    io.lettuce: INFO
    reactor.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} %-5level - %msg%n"
#    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ds-socket-login.log
    max-size: 100MB
    max-history: 30

# Application Specific Configuration
app:
  name: "DS Socket Login System"
  version: "1.0.0"
  description: "SocketIO Redis Cache System with Netty and WebFlux"

  # Performance Settings
  performance:
    max-concurrent-connections: 10000
    connection-pool-size: 50
    thread-pool-size: 100

  # Security Settings
  security:
    token-expiry: 3600 # 1 hour in seconds
    max-login-attempts: 5
    rate-limit:
      requests-per-minute: 100
      burst-capacity: 200

#WSO2 configuration
wso2:
  base-url: https://testid.shinhansec.com.vn/t/testopenapi.shinhansec.com.vn         # hoặc https://apim.example.com
  token-endpoint: /oauth2/token
  introspect-endpoint: /oauth2/introspect
  super-user: <EMAIL>
  super-password: Ahboish@5oHasei8b
  client-id: uBGdgygLDflIOIQbauwwIu_qfy0a
  client-secret: zOm6YIWc79wPzK1qd14eLfImDKOVecAElSyIcrp_ptUa
  scope: openid profile email            # tuỳ app
  jwks-endpoint: /.well-known/jwks.json    # IS 5.11+; APIM: /oauth2/jwks
  timeout-ms: 15000

#---
## Development Profile
#spring:
#  config:
#    activate:
#      on-profile: dev
#  data:
#    redis:
#      host: *************
#      port: 6380
#
#logging:
#  level:
#    com.example.dssocketlogin: DEBUG
#    root: INFO
#
#nodejs:
#  server:
#    socket-url: "wss://test.shinhansec.com.vn"
#
#---
## Production Profile
#spring:
#  config:
#    activate:
#      on-profile: prod
#  data:
#    redis:
#      host: ${REDIS_HOST:redis-server}
#      port: ${REDIS_PORT:6380}
#      password: ${REDIS_PASSWORD:}
#
#logging:
#  level:
#    com.example.dssocketlogin: INFO
#    root: WARN
#
#nodejs:
#  server:
#    socket-url: ${NODEJS_SERVER_URL:wss://test.shinhansec.com.vn}
#
#socketio:
#  server:
#    hostname: ${SOCKETIO_HOST:wss://test.shinhansec.com.vn}
#    port: ${SOCKETIO_PORT:9092}
