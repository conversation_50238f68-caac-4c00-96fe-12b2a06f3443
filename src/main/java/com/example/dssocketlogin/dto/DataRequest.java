package com.example.dssocketlogin.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Khớp 1–1 với JSON mẫu phía client.
 * - Giữ nguyên tên field JSON viết hoa bằng @JsonProperty
 * - metadata là object con (timestamp, userAgent)
 * - Có cả sessionId (lowerCamel) và SessionID (UpperCamel) vì JSON đang có cả 2 key
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataRequest {

    // ----- <PERSON><PERSON><PERSON> bạn đang gửi ở đầu payload -----
    @NotBlank(message = "Message ID is required")
    private String messageId;

    @NotBlank(message = "Session ID is required")
    private String sessionId;

//    @NotBlank(message = "Access token is required")
    private String accessToken;

//    private Metadata metadata; // { timestamp, userAgent }

    // ----- Các trường đặc thù hệ thống (giữ nguyên key bằng @JsonProperty) -----

    @JsonProperty("CltVersion")
    private String cltVersion;

    @JsonProperty("ClientSeq")
    private Integer clientSeq;

    @JsonProperty("SecCode")
    private String secCode;

    @JsonProperty("WorkerName")
    private String workerName;

    @JsonProperty("ServiceName")
    private String serviceName;

    @JsonProperty("TimeOut")
    private Integer timeOut;

    @JsonProperty("MWLoginID")
    private String mwLoginID;

    @JsonProperty("MWLoginPswd")
    private String mwLoginPswd;

    @JsonProperty("AppLoginID")
    private String appLoginID;

    @JsonProperty("ClientSentTime")
    private String clientSentTime;

    @JsonProperty("Lang")
    private String lang;

    @JsonProperty("MdmTp")
    private String mdmTp;

    @JsonProperty("InVal")
    private List<String> inVal;

    @JsonProperty("TotInVal")
    private Integer totInVal;

    @JsonProperty("AprStat")
    private String aprStat;

    @JsonProperty("Operation")
    private String operation;

    @JsonProperty("CustMgnBrch")
    private String custMgnBrch;

    @JsonProperty("CustMgnAgc")
    private String custMgnAgc;

    @JsonProperty("BrkMgnBrch")
    private String brkMgnBrch;

    @JsonProperty("BrkMgnAgc")
    private String brkMgnAgc;

    @JsonProperty("LoginBrch")
    private String loginBrch;

    @JsonProperty("LoginAgnc")
    private String loginAgnc;

    @JsonProperty("AprSeq")
    private String aprSeq;

    @JsonProperty("MakerDt")
    private String makerDt;

    @JsonProperty("AprID")
    private String aprID;

    @JsonProperty("AprAmt")
    private String aprAmt;

    @JsonProperty("Otp")
    private String otp;

    @JsonProperty("AcntNo")
    private String acntNo;

    @JsonProperty("SubNo")
    private String subNo;

    @JsonProperty("BankCd")
    private String bankCd;

    @JsonProperty("PCName")
    private String pcName;

    // JSON của bạn có thêm key "SessionID" (UpperCamel) rỗng ở cuối
    @JsonProperty("SessionID")
    private String sessionID;

    // ====== LỚP CON: metadata ======
//    @Data
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class Metadata {
//        @NotNull
//        private Long timestamp;   // Date.now() từ client (ms)
//        @NotBlank
//        private String userAgent; // navigator.userAgent
//    }
    private String action;
    private Map<String, Object> payload;
    private Map<String, Object> metadata;

    private static final ObjectMapper MAPPER = new ObjectMapper()
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    private static String stringify(Object v) {
        if (v == null) return null;
        if (v instanceof String || v instanceof Number || v instanceof Boolean) {
            return String.valueOf(v);
        }
        try {
            // Object/List/Map -> JSON string
            return MAPPER.writeValueAsString(v);
        } catch (Exception e) {
            return String.valueOf(v);
        }
    }

    /**
     * Map<String, String> (shallow):
     * - Tên key tuân theo @JsonProperty (vd "CltVersion", "ClientSeq"...)
     * - Map/list sẽ được stringify thành JSON.
     * - Bỏ qua null (includeNulls = false).
     */
    public Map<String, String> toStringMap() {
        return toStringMap(false);
    }

    public Map<String, String> toStringMap(boolean includeNulls) {
        Map<String, Object> objMap = MAPPER.convertValue(this, new TypeReference<Map<String, Object>>() {});
        Map<String, String> out = new LinkedHashMap<>();
        for (Map.Entry<String, Object> e : objMap.entrySet()) {
            if (e.getValue() == null && !includeNulls) continue;
            out.put(e.getKey(), stringify(e.getValue()));
        }
        return out;
    }

    /**
     * Map<String, String> (flat):
     * - Flatten các Map con ở level 1 (vd. metadata.timestamp -> "metadata.timestamp")
     * - List sẽ stringify JSON.
     */
    public Map<String, Object> toFlatStringMap() {
        Map<String, Object> objMap = MAPPER.convertValue(this, new TypeReference<Map<String, Object>>() {});
        Map<String, Object> out = new LinkedHashMap<>();
        for (Map.Entry<String, Object> e : objMap.entrySet()) {
            String k = e.getKey();
            Object v = e.getValue();
            if (v == null) continue;
            if(Set.of("messageId", "sessionId","metadata").contains(k)) continue;
            if (v instanceof Map<?, ?> m) {
                // flatten 1 cấp
                for (Map.Entry<?, ?> sub : m.entrySet()) {
                    if (sub.getKey() == null) continue;
                    String subKey = k + "." + sub.getKey().toString();
                    out.put(subKey,sub.getValue());
                }
            } else {
                out.put(k, v);
            }
        }
        return out;
    }

}
