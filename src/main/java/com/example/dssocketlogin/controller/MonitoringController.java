//package com.example.dssocketlogin.controller;
//
//import com.example.dssocketlogin.model.UserSession;
//import com.example.dssocketlogin.service.NodeJsIntegrationService;
//import com.example.dssocketlogin.service.SessionService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.MediaType;
//import org.springframework.web.bind.annotation.*;
//import reactor.core.publisher.Flux;
//import reactor.core.publisher.Mono;
//
//import java.time.LocalDateTime;
//import java.util.List;
//import java.util.Map;
//
//@Slf4j
//@RestController
//@RequestMapping("/api/monitoring")
//@RequiredArgsConstructor
//public class MonitoringController {
//
//    private final SessionService sessionService;
//    private final NodeJsIntegrationService nodeJsIntegrationService;
//
//    @Value("${app.name}")
//    private String appName;
//
//    @Value("${app.version}")
//    private String appVersion;
//
//    @GetMapping("/health")
//    public Mono<Map<String, Object>> health() {
//        return nodeJsIntegrationService.checkNodeJsHealth()
//                .map(nodeJsHealthy -> Map.of(
//                    "status", "UP",
//                    "timestamp", LocalDateTime.now(),
//                    "application", Map.of(
//                        "name", appName,
//                        "version", appVersion,
//                        "status", "UP"
//                    ),
//                    "dependencies", Map.of(
//                        "redis", Map.of(
//                            "status", "UP",
//                            "description", "Redis connection is healthy"
//                        ),
//                        "nodejs", Map.of(
//                            "status", nodeJsHealthy ? "UP" : "DOWN",
//                            "description", nodeJsHealthy ? "Node.js server is healthy" : "Node.js server is not responding"
//                        )
//                    )
//                ))
//                .onErrorReturn(Map.of(
//                    "status", "DOWN",
//                    "timestamp", LocalDateTime.now(),
//                    "error", "Health check failed"
//                ));
//    }
//
//    @GetMapping("/stats")
//    public Mono<Map<String, Object>> stats() {
//        return Mono.zip(
//                sessionService.getActiveSessionCount(),
//                nodeJsIntegrationService.getNodeJsStats()
//        ).map(tuple -> {
//            Long activeSessionCount = tuple.getT1();
//            Map<String, Object> nodeJsStats = tuple.getT2();
//
//            return Map.of(
//                "timestamp", LocalDateTime.now(),
//                "sessions", Map.of(
//                    "active", activeSessionCount,
//                    "total", activeSessionCount // For now, same as active
//                ),
//                "system", Map.of(
//                    "uptime", System.currentTimeMillis(),
//                    "memory", Map.of(
//                        "total", Runtime.getRuntime().totalMemory(),
//                        "free", Runtime.getRuntime().freeMemory(),
//                        "used", Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory(),
//                        "max", Runtime.getRuntime().maxMemory()
//                    ),
//                    "threads", Thread.activeCount()
//                ),
//                "nodejs", nodeJsStats
//            );
//        });
//    }
//
//    @GetMapping("/sessions")
//    public Flux<UserSession> getAllSessions() {
//        return sessionService.getAllSessions()
//                .doOnSubscribe(subscription -> log.debug("Fetching all sessions"))
//                .doOnComplete(() -> log.debug("Completed fetching all sessions"));
//    }
//
//    @GetMapping("/sessions/{sessionId}")
//    public Mono<Map<String, Object>> getSession(@PathVariable String sessionId) {
//        return Mono.fromCallable(() -> sessionService.getSession(sessionId))
//                .map(session -> {
//                    if (session == null) {
//                        return Map.<String, Object>of(
//                            "found", false,
//                            "sessionId", sessionId,
//                            "message", "Session not found"
//                        );
//                    }
//                    return Map.<String, Object>of(
//                        "found", true,
//                        "session", session
//                    );
//                })
//                .doOnSubscribe(subscription -> log.debug("Fetching session: {}", sessionId));
//    }
//
//    @GetMapping("/users/{userId}/sessions")
//    public Mono<Map<String, Object>> getUserSessions(@PathVariable String userId) {
//        return Mono.fromCallable(() -> sessionService.getUserSessions(userId))
//                .map(sessions -> Map.<String, Object>of(
//                    "userId", userId,
//                    "sessionCount", sessions.size(),
//                    "sessions", sessions
//                ))
//                .doOnSubscribe(subscription -> log.debug("Fetching sessions for user: {}", userId));
//    }
//
//    @PostMapping("/sessions/{sessionId}/disconnect")
//    public Mono<Map<String, Object>> disconnectSession(@PathVariable String sessionId) {
//        return Mono.fromCallable(() -> {
//            UserSession session = sessionService.getSession(sessionId);
//            if (session == null) {
//                return Map.<String, Object>of(
//                    "success", false,
//                    "message", "Session not found"
//                );
//            }
//
//            sessionService.disconnectBySocketId(session.getSocketId());
//            return Map.<String, Object>of(
//                "success", true,
//                "message", "Session disconnected successfully",
//                "sessionId", sessionId
//            );
//        })
//        .doOnSubscribe(subscription -> log.info("Disconnecting session: {}", sessionId));
//    }
//
//    @PostMapping("/cleanup/expired")
//    public Mono<Map<String, Object>> cleanupExpiredSessions() {
//        return Mono.fromCallable(() -> {
//            sessionService.cleanupExpiredSessions();
//            return Map.<String, Object>of(
//                "success", true,
//                "message", "Expired sessions cleanup completed",
//                "timestamp", LocalDateTime.now()
//            );
//        })
//        .doOnSubscribe(subscription -> log.info("Starting expired sessions cleanup"));
//    }
//
//    @GetMapping(value = "/sessions/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
//    public Flux<Map<String, Object>> streamSessionStats() {
//        return Flux.interval(java.time.Duration.ofSeconds(5))
//                .flatMap(tick -> sessionService.getActiveSessionCount()
//                        .map(count -> Map.<String, Object>of(
//                            "timestamp", LocalDateTime.now(),
//                            "activeSessionCount", count,
//                            "tick", tick
//                        )))
//                .doOnSubscribe(subscription -> log.debug("Starting session stats stream"))
//                .doOnCancel(() -> log.debug("Session stats stream cancelled"));
//    }
//
//    @GetMapping("/metrics")
//    public Mono<Map<String, Object>> getMetrics() {
//        return sessionService.getActiveSessionCount()
//                .map(activeSessionCount -> {
//                    Runtime runtime = Runtime.getRuntime();
//                    long totalMemory = runtime.totalMemory();
//                    long freeMemory = runtime.freeMemory();
//                    long usedMemory = totalMemory - freeMemory;
//                    long maxMemory = runtime.maxMemory();
//
//                    return Map.of(
//                        "timestamp", LocalDateTime.now(),
//                        "sessions", Map.of(
//                            "active", activeSessionCount
//                        ),
//                        "jvm", Map.of(
//                            "memory", Map.of(
//                                "used", usedMemory,
//                                "free", freeMemory,
//                                "total", totalMemory,
//                                "max", maxMemory,
//                                "usage_percentage", (double) usedMemory / maxMemory * 100
//                            ),
//                            "threads", Map.of(
//                                "active", Thread.activeCount(),
//                                "daemon", Thread.getAllStackTraces().keySet().stream()
//                                        .mapToInt(t -> t.isDaemon() ? 1 : 0).sum()
//                            ),
//                            "gc", Map.of(
//                                "note", "GC stats would be collected here"
//                            )
//                        ),
//                        "system", Map.of(
//                            "uptime", System.currentTimeMillis(),
//                            "processors", Runtime.getRuntime().availableProcessors()
//                        )
//                    );
//                });
//    }
//}
