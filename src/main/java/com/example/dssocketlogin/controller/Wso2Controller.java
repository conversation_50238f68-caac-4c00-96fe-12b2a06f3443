package com.example.dssocketlogin.controller;


import com.example.dssocketlogin.service.wso2.Wso2Service;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/wso2")
@RequiredArgsConstructor
public class Wso2Controller {
   /**
    * 1. API tạo tài khoản WSO2
    * 2. API đăng nhập
    * 3. API kiểm tra token /refresh
    * 4. API kiểm tra tài khoản tồn tại
    * 5. API Xóa tài khoản
    */
   private final Wso2Service wso2Service;

    @PostMapping("/login")
    public Mono<JsonNode> loginWso2() {
      Mono<JsonNode> result =  wso2Service.loginWso2("nhacvb","Shinhan@1");
      return result;
    }

   @PostMapping("/create-account")
   public Mono<Map<String, Object>> createAccountWso2() {
      return null;
   }

   @PostMapping("/check-token")
   public Mono<Map<String, Object>> checkTokenWso2() {
      return null;
   }

   @PostMapping("/check-exist-account")
   public Mono<Map<String, Object>> checkExistAccountWso2() {
      return null;
   }


   @PostMapping("/delete-account")
   public Mono<Map<String, Object>> deleteAccountWso2() {
      return null;
   }

}
