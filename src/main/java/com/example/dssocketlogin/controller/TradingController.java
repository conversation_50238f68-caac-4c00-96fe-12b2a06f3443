package com.example.dssocketlogin.controller;

import com.example.dssocketlogin.model.global.RequestTrading;
import com.example.dssocketlogin.service.TradingService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

@RestController
@RequestMapping("/api/trading")
@Slf4j
@RequiredArgsConstructor
public class TradingController {
   private final TradingService tradingService;

   @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
   public Mono<ResponseEntity<Map<String,Object>>> fosRequest(@RequestHeader Map<String, String> headers, @RequestBody RequestTrading requestTrading){
      String auth = headers.get("Authorization");
      if(auth == null){
         try {
            // Nếu trường hợp login thì tạo ra connection non - authen
            // 1. Mở thêm một luồng socket -> gọi API non-authen | sẽ thực hiện trên 1 luồng socket (Kiểm tra uuid cso trùng không)



            return tradingService.fetchAPINonAuthen(requestTrading);
         } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
         }
      }
      String token = auth.startsWith("Bearer ") ? auth.substring(7) : auth;
      // Kiểm tra header authenicate
      return tradingService.fetchAPIAuthen(requestTrading);
   }
}
