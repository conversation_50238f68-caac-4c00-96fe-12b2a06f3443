package com.example.dssocketlogin.service;

import com.corundumstudio.socketio.SocketIOClient;
import com.example.dssocketlogin.model.*;
import io.socket.client.Socket;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class SessionService {

    // 1. Danh sách Kết nối không authen
    private final RedisTemplate<String, AnonymousSession> anonymousSessionRedisTemplate;
    private final RedisTemplate<String, UserFosSession> listUserFosSessionRedisTemplate;

    // 2. <PERSON>h sách kết nối có authen
    private final RedisTemplate<String, Map<String, UserFosSession>> userChannelRedisTemplate;

    // 0000287001 , <String, Channel>
    // <0000287001, <02, UserInfo>> // mỗi channel chỉ được một thiết bị authen -> non-authen unlimited  (Cần cơ chế quản non-authe)
    //

    private final @Lazy NodeJsSocketConnectionManager nodeJsSocketManager; // đã lazy

    @Getter
    @Setter
    private final Map<String, SocketIOClient> socketConnectionsMiddle = new ConcurrentHashMap<>();

    @Value("${session.default-timeout}")
    private int defaultTimeoutSeconds;

    @Value("${session.redis-key-prefix}")
    private String keyPrefix;

    @Value("${session.max-devices-per-channel}")
    private int maxDevicesPerChannel;

    // ==================== ANONYMOUS METHODS ====================
    /**
     * Create anonymous session: fos:sid:{socketId}
     * This is called when client connects but hasn't authenticated yet
     * Tạo 02 kết nối: 1 kết nối java + web | 1 kết nối java + nodejs code
     *
     */
    public AnonymousSession createAnonymousSession(String socketId, String clientIp, String userAgent, String channel) {
        try {
            AnonymousSession session = AnonymousSession.create(socketId, clientIp, userAgent, channel);
            String key = getSocketKey(socketId);
            log.info("[CRE] Anonymous session created for socket: {} on channel: {}", socketId, channel);
            // Store anonymous session in Redis with TTL
            anonymousSessionRedisTemplate.opsForValue().set(key, session, defaultTimeoutSeconds, TimeUnit.SECONDS);
            // Tạo mới nodejs
            nodeJsSocketManager.createConnectionForAnonymousSession(session);
            return session;
        } catch (Exception e) {
            log.error("[CRE] Failed to create anonymous session for socket: {}", socketId, e);
            throw new RuntimeException("Failed to create anonymous session", e);
        }
    }

    /**
     * Get anonymous sesson by socketId: fos:sid:{socketId}
     */
    public AnonymousSession getAnonymousSession(String socketId) {
        try {
            String key = getSocketKey(socketId);
            AnonymousSession session = anonymousSessionRedisTemplate.opsForValue().get(key);

            if (session != null && session.isExpired()) {
                log.debug("Anonymous session expired for socket: {}", socketId);
                deleteAnonymousSession(socketId);
                return null;
            }

            return session;

        } catch (Exception e) {
            log.error("Failed to get anonymous session for socket: {}", socketId, e);
            return null;
        }
    }

    /**
     * Delete anonymous session
     */
    private void deleteAnonymousSession(String socketId) {
        try {
            String key = getSocketKey(socketId);
            anonymousSessionRedisTemplate.delete(key);
            log.debug("Deleted anonymous session for socket: {}", socketId);
        } catch (Exception e) {
            log.error("Failed to delete anonymous session: {}", socketId, e);
        }
    }

    // ==================== AUTHENICATE  METHODS ====================
    /**
     * Authenticate user and move from anonymous session to user device list
     * This creates/updates fos:uid:{userId} and removes fos:sid:{socketId}
     */
    public UserFosSession createAuthenticateUser(UserFosSession currentUserConn) {
        String socketId = "";
        String userId = "";
        UserFosSession lastUserConn;
        try {
            if(currentUserConn == null) return null;
            socketId = currentUserConn.getSidMiddle();
            userId =  currentUserConn.getUserId();
            // 1. kiểm tra và lấy session hiện tại theo socket_id.
            AnonymousSession anonymousSession = getAnonymousSession(socketId);
            if (anonymousSession == null) {
                throw new RuntimeException("Anonymous session not found for socket: " + socketId);
            }
            // 2. Kiểm tra socket_id java -> socket nodejs(FOSGateway)
            var socketConnCore = nodeJsSocketManager.getSocketConnectionsCore().get(socketId);
            if(socketConnCore == null) {
                // Không có tồn tại kết nối socketId
                currentUserConn.setStateConnCore(false);
                return null;
            }else{
                currentUserConn.setStateConnCore(true);
            }

            // 3. Thêm vào danh sách
            // Get or create user connect all channel -> tạo redis user
            try {
                String keyUidRedis = getUserKey(userId);
                Map<String, UserFosSession> userConn = userChannelRedisTemplate.opsForValue().get(keyUidRedis);
                // Không có thông tin và tạo lưu redis cache
                if (userConn == null) {
                    log.debug("No connection list found for user: {}", userId);
                    userConn = new HashMap<>();
                    userConn.put(currentUserConn.getMdmTp(), currentUserConn);
                    userChannelRedisTemplate.opsForValue().set(keyUidRedis, userConn);
                } else {
                    if(userConn.get(currentUserConn.getMdmTp()) != null) {
                        // kiểm tra socketId khác nhau không | device_id + sessionID giống nhau thì cập nhật lại
                        lastUserConn = userConn.get(currentUserConn.getMdmTp());
                        // cập nhật lại session authen
                        // cùng kênh thì đăng xuất socketID trước đó.

                        // xóa kết nối core
                        disconnectBySocketId(lastUserConn.getSidMiddle());
//                        Socket lastSocket = nodeJsSocketManager.getSocketConnectionsCore().get(lastUserConn.getSidMiddle());
//                        if(lastSocket != null){
//                            lastSocket.disconnect();
//                            nodeJsSocketManager.getSocketConnectionsCore().remove(lastUserConn.getSidMiddle());
//                        }
//                        // xóa kết nối
//                        SocketIOClient socketIOClient = socketConnectionsMiddle.get(lastUserConn.getSidMiddle());
//                        if(socketIOClient != null){
//                            socketIOClient.disconnect();
//                            socketConnectionsMiddle.remove(socketId);
//                        }
                        // xóa socket
                        userConn.put(currentUserConn.getMdmTp(), currentUserConn);
                        userChannelRedisTemplate.opsForValue().set(keyUidRedis, userConn);
                        log.info("lastUserConn: {} | currentUserConn: {}", lastUserConn, currentUserConn);
                    }else {
                        userConn.put(currentUserConn.getMdmTp(), currentUserConn);
                        userChannelRedisTemplate.opsForValue().set(keyUidRedis, userConn);
                    }
                }
            } catch (Exception e) {
                log.error("Failed to get user list: {}", userId, e);
            }

            // 4. Thực hiện chuyển anonymous sang authenicate
            // 4.1. Remove anonymous session (user is now authenticated)
            deleteAnonymousSession(socketId);
            // 4.2. tạo lại socket authen fos:sid không có tiền tố anonymous_xxxxxxxxxxxxxxxxxxxx
            listUserFosSessionRedisTemplate.opsForValue().set(getSocketKey(socketId), currentUserConn);
            log.info("User authenticated new: {} on channel: {} with socket: {}", userId, currentUserConn.getMdmTp(), socketId);
            return currentUserConn;
        } catch (Exception e) {
            log.error("Failed to authenticate user: {} for socket: {}", userId, socketId, e);
            throw new RuntimeException("Failed to authenticate user", e);
        }
    }

    /**
     * Get user connection in list: fos:uid:{userId}
     */
    public UserFosSession getUserId(String userId, String mdmTp) {
        try {
            String key = getUserKey(userId);
            Map<String, UserFosSession> userConn = userChannelRedisTemplate.opsForValue().get(key);

            if (userConn == null) {
                log.debug("No user connection list found for user: {}", userId);
                return null;
            }
            // Lấy thông tin theo channel
            return userConn.get(mdmTp);
        } catch (Exception e) {
            log.error("Failed to get user device list: {}", userId, e);
            return null;
        }
    }

    /**
     * Xóa socket ID tồn tại trên redis
     * fos:sid:{socketId}
     * fos:uid:{userId}
     */
    public void disconnectBySocketId(String socketId) {
        try {
            String keySid = getSocketKey(socketId);
            UserFosSession userFosSession = listUserFosSessionRedisTemplate.opsForValue().get(keySid);
            if (userFosSession != null) {
                String userId = userFosSession.getUserId();
                String channel = userFosSession.getMdmTp();
                String keyUidRedis = getUserKey(userId);
                Map<String, UserFosSession> listUserConn = userChannelRedisTemplate.opsForValue().get(keyUidRedis);
                if(listUserConn != null) {
                    listUserConn.remove(channel);
                    if(listUserConn.isEmpty()){
                        userChannelRedisTemplate.delete(keyUidRedis);
                    }else{
                        userChannelRedisTemplate.opsForValue().set(keyUidRedis, listUserConn);
                    }
                }
            }
            // kích socket middle + core
            if(socketConnectionsMiddle.get(socketId) !=null) {
                socketConnectionsMiddle.get(socketId).disconnect();
                socketConnectionsMiddle.remove(socketId);
            }
            if(nodeJsSocketManager.getSocketConnectionsCore().get(socketId) !=null) {
                nodeJsSocketManager.getSocketConnectionsCore().get(socketId).disconnect();
                nodeJsSocketManager.getSocketConnectionsCore().remove(socketId);
            }
            listUserFosSessionRedisTemplate.delete(keySid);
            log.info("[PUB] Ngắt kết nối: {}", socketId);
        } catch (Exception e) {
            log.info("[PUB] Lỗi ngắt kết nối: {}", socketId, e);
        }

    }

    // ==================== KEY GENERATION METHODS ====================
    /**
     * Generate Redis key for socket: fos:sid:{socketId}
     */
    private String getSocketKey(String socketId) {
        return keyPrefix + "sid:" + socketId;
    }

    /**
     * Generate Redis key for user: fos:uid:{userId}
     */
    private String getUserKey(String userId) {
        return keyPrefix + "uid:" + userId;
    }


}

