package com.example.dssocketlogin.service.demo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserSocketMeta implements Serializable {
   private String instId;     // JVM hiện tại
   private String socketId;   // io.socket client id
   private long createdAt;
   private long lastSeen;
   private String sessionId;  // fos:sid (tuỳ)
   private String mdmTp;      // channel/kênh: "02" | WTS/HTS/MTS...
   private String userId;     // người dùng
}
