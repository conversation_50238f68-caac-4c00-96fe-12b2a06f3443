package com.example.dssocketlogin.service.demo;

import io.socket.client.Socket;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class SocketConnManagement {

   // ======================== Thực hiện theo rules ===========//
   // Socket chung cho No-Authen
   private volatile Socket sharedSocket;
   private volatile Mono<Socket> sharedConnectingMono;

   // Socket theo user/thiết bị (sau login)
   private final Map<String /*userConnId*/, Socket> userSockets = new ConcurrentHashMap<>();
   private final Map<String /*userConnId*/, Mono<Socket>> userConnecting = new ConcurrentHashMap<>();

   // Pending request chờ RES_MSG (có thể dùng chung nếu ClientSeq là duy nhất)
   private final Map<String /*ClientSeq*/, Sinks.One<Map<String,Object>>> pending = new ConcurrentHashMap<>();

   // (khuyến nghị) Thêm TTL/lastActivity nếu muốn auto-clean
   private final Map<String /*userConnId*/, Long /*lastActivityMillis*/> lastSeen = new ConcurrentHashMap<>();

   // quản lý cache
   private final Map<String /*userId (FOS_ID) */, Map<String /*channel(mdmTp)*/, Object>> listCacheAll = new ConcurrentHashMap<>();
   // Object socketID, JVM hiện tại


//   // ====== Hàm tạo key redis ===== //
//   private String K_USER_CONN() { return "fos:user:conn"; }
//   private String K_IDX_USER(String userId) { return "fos:user:idx:" + userId; }
//   private String K_IDX_USER_CH(String userId, String ch) { return "fos:user:idx:" + userId + ":ch:" + ch; }
//   private String K_BY_SOCKET(String socketId) { return "fos:user:conn:bySocket:" + socketId; }


}
