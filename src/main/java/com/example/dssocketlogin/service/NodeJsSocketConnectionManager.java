package com.example.dssocketlogin.service;

import com.corundumstudio.socketio.SocketIOServer;
import com.example.dssocketlogin.model.*;
import com.example.dssocketlogin.model.nodejs.ResponseNode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.socket.client.IO;
import io.socket.client.Socket;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.net.URI;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * Manages Socket.IO connections from Java to Node.js server
 * Each client connection to Java creates a corresponding connection to Node.js
 * Uses shared fos:sid for connection management
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NodeJsSocketConnectionManager {

   @Value("${nodejs.server.socket-url:wss://test.shinhansec.com.vn}")
   private String nodeJsServerUrl;

   @Value("${nodejs.socket.namespace:/services}")
   private String socketNamespace;

   @Value("${nodejs.socket.reconnect-attempts:5}")
   private int reconnectAttempts;

   @Value("${nodejs.socket.reconnect-delay:1000}")
   private long reconnectDelay;

   //
//   private final List<Object> list;


   // Removed SessionService dependency to avoid circular reference
   private final SocketIOServer socketIOServer;

   // Thông tin tạo kết cache kêt nối
   private final ObjectProvider<SessionService> sessionServiceProvider;
   private SessionService sessionService() {
      return sessionServiceProvider.getObject();
   }


   // Map to store Socket.IO connections: socketId -> Socket
   @Getter
   @Setter
   private final Map<String, Socket> socketConnectionsCore = new ConcurrentHashMap<>();

   @Getter
   @Setter
   private Socket connNonAuthen;


   // Map to store connection metadata: socketId -> ConnectionInfo
   @Getter
   @Setter
   private final Map<String, NodeJsConnectionInfo> connectionInfoMap = new ConcurrentHashMap<>();
   private final ObjectMapper OM = new ObjectMapper();
   private final Map<String, Sinks.One<Map<String,Object>>> pending = new ConcurrentHashMap<>();
   private static final Object LOCK = new Object();
   private volatile Mono<Socket> connectingMono;


   /**
    * Initialize the connection manager
    */
   @PostConstruct
   public void init() {
      log.info("Initializing Node.js Socket Connection Manager");
//      startConnectionMonitoring();
      log.info("Node.js Socket Connection Manager initialized successfully");
   }

   // tạo kết nối non-authen
   public Mono<Socket>  creatConnNonAuthen(){
      Socket s = connNonAuthen;
      if (s != null && s.connected()) return Mono.just(s);
      synchronized (LOCK) {
         if (connectingMono != null) return connectingMono;
         Sinks.One<Socket> sink = Sinks.one();
         connectingMono = sink.asMono()
                 .doFinally(sig -> { synchronized (LOCK) { connectingMono = null; }});

         // tạo mới
//         String socketId = UUID.randomUUID().toString();
         // Configure Socket.IO client options
         IO.Options options = IO.Options.builder()
                 .setPath("/services")
                 .setReconnection(true)
                 .setReconnectionAttempts(reconnectAttempts)
                 .setReconnectionDelay(reconnectDelay)
                 .setTimeout(15000)
                 .build();
         Socket socket = IO.socket(URI.create(nodeJsServerUrl), options);
         // ===== Handlers =====
         socket.on(Socket.EVENT_CONNECT, args -> {
            connNonAuthen = socket;
            sink.tryEmitValue(socket); // báo "đã sẵn sàng"
         });

         socket.on(Socket.EVENT_DISCONNECT, args -> {
            connNonAuthen = null;
            // fail toàn bộ request đang chờ
            pending.forEach((id, sk) -> sk.tryEmitError(new IllegalStateException("Socket disconnected")));
            pending.clear();
         });

         socket.on(Socket.EVENT_CONNECT_ERROR, args -> {
            String err = (args != null && args.length > 0) ? String.valueOf(args[0]) : "unknown";
            sink.tryEmitError(new IllegalStateException("Connect error: " + err));
         });

         socket.on("RES_MSG", (a) -> {
            if (a == null || a.length == 0) return;
            Object payload = a[0];
            Map<String,Object> body = (payload instanceof org.json.JSONObject jo)
                    ? jo.toMap()
                    : OM.convertValue(payload, new com.fasterxml.jackson.core.type.TypeReference<Map<String,Object>>() {});
            Object rid = body.get("ClientSeq");
            if (rid == null) return;
            Sinks.One<Map<String,Object>> reqSink = pending.remove(rid.toString());
            if (reqSink != null) reqSink.tryEmitValue(body);
         });
         socket.connect(); // async

         return connectingMono;
      }
   }


   // tạo kết nối cho hàm đăng nhập
   private LinkedHashMap<String, Socket> listConnAuthen = new LinkedHashMap<>();

   public Mono<Socket> creatConnLogin(){
      synchronized (LOCK) {
         if (connectingMono != null) return connectingMono;
         Sinks.One<Socket> sink = Sinks.one();
         connectingMono = sink.asMono()
                 .doFinally(sig -> { synchronized (LOCK) { connectingMono = null; }});

         // tạo mới
//         String socketId = UUID.randomUUID().toString();
         // Configure Socket.IO client options
         IO.Options options = IO.Options.builder()
                 .setPath("/services")
                 .setReconnection(true)
                 .setReconnectionAttempts(reconnectAttempts)
                 .setReconnectionDelay(reconnectDelay)
                 .setTimeout(15000)
                 .build();
         Socket socket = IO.socket(URI.create(nodeJsServerUrl), options);
         // ===== Handlers =====
         socket.on(Socket.EVENT_CONNECT, args -> {
            connNonAuthen = socket;
            sink.tryEmitValue(socket); // báo "đã sẵn sàng"
         });

         socket.on(Socket.EVENT_DISCONNECT, args -> {
            connNonAuthen = null;
            // fail toàn bộ request đang chờ
            pending.forEach((id, sk) -> sk.tryEmitError(new IllegalStateException("Socket disconnected")));
            pending.clear();
         });

         socket.on(Socket.EVENT_CONNECT_ERROR, args -> {
            String err = (args != null && args.length > 0) ? String.valueOf(args[0]) : "unknown";
            sink.tryEmitError(new IllegalStateException("Connect error: " + err));
         });

         socket.on("RES_MSG", (a) -> {
            if (a == null || a.length == 0) return;
            Object payload = a[0];
            Map<String,Object> body = (payload instanceof org.json.JSONObject jo)
                    ? jo.toMap()
                    : OM.convertValue(payload, new com.fasterxml.jackson.core.type.TypeReference<Map<String,Object>>() {});
            Object rid = body.get("ClientSeq");
            if (rid == null) return;
            Sinks.One<Map<String,Object>> reqSink = pending.remove(rid.toString());
            if (reqSink != null) reqSink.tryEmitValue(body);
         });
         socket.connect(); // async

         return connectingMono;
      }
   }

   /**
    * Create Socket.IO connection to Node.js for anonymous session
    */
   public void createConnectionForAnonymousSession(AnonymousSession session) {
      String socketId = session.getSocketId();
      try {
         if (socketConnectionsCore.containsKey(socketId)) {
            log.warn("[CRE-NODE] Socket connection already exists for socketId: {}", socketId);
            return;
         }

         // Configure Socket.IO client options
         IO.Options options = IO.Options.builder()
                 .setPath("/services")
                 .setReconnection(true)
                 .setReconnectionAttempts(reconnectAttempts)
                 .setReconnectionDelay(reconnectDelay)
                 .setTimeout(15000)
                 .build();

         // Create Socket.IO client connection
         Socket socket = IO.socket(URI.create(nodeJsServerUrl), options);

         // Setup event handlers
         setupSocketEventHandlers(socket, session);

         // 1. Thực hiện lưu thông tin không phân biệt authen
         socketConnectionsCore.put(socketId, socket); // khi ngắt kết nối cần xóa cache
         // Connect to Node.js
         socket.connect();  
         log.info("[CRE-NODE] Created anonymous session: {} channel: {}", socketId, session.getChannel());
      } catch (Exception e) {
         log.error("[CRE-NODE][ERROR] Connection for Nodejs is failed with anonymous socketId: {} channel: {}", socketId, session.getChannel(), e);
      }
   }

   /**
    * Setup Socket.IO event handlers
    */
   private void setupSocketEventHandlers(Socket socket, AnonymousSession session) {
      String socketId = session.getSocketId();
      // Connection events
      socket.on(Socket.EVENT_CONNECT, args -> {
         log.info("[RECEIVED-NODE] Connected to Node.js server for socketId: {}", socketId);

         // Send initial session info
         Map<String, Object> sessionData = Map.of(
                 "socketId", socketId,
                 "sessionId", session.getSessionId(),
                 "channel", session.getChannel(),
                 "clientIp", session.getClientIp(),
                 "userAgent", session.getUserAgent(),
                 "timestamp", System.currentTimeMillis()
         );

         socket.emit("session_init", sessionData);
      });
      socket.on(Socket.EVENT_DISCONNECT, args -> {
         log.info("[RECEIVED-NODE] Disconnected from Node.js server for socketId: {}", socketId);
         // thực hiện xóa socketID trong danh sách cache tạm!
      });

      socket.on(Socket.EVENT_CONNECT_ERROR, args -> {
         log.error("[RECEIVED-NODE] Connection error to Node.js server for socketId: {} - {}", socketId, args[0]);
      });

      // Chuẩn Nodejs -> Client
      socket.on("RES_MSG", (args) -> {
         if (args == null || args.length == 0) return;
         Object payload = args[0];
         log.debug("[RECEIVED-NODE] Received data response from Node.js for socketId: {} | {}", socketId, payload);
         // Forward response back to client via SocketIO server
         sendToFOSTrading(socketId, payload);
      });
   }
   /**
    * Send data to Node.js via Socket.IO
    */
   public void sendToNodeJs(String socketId, String event, Object data) {
      try {
         Socket socket = socketConnectionsCore.get(socketId);
         if (socket != null && socket.connected()) {
            String os = new ObjectMapper().writeValueAsString(data);
            socket.emit(event, os);
            log.debug("Sent event '{}' to Node.js for socketId: {}", event, socketId);
         } else {
            log.warn("No active Node.js connection for socketId: {}", socketId);
         }
      } catch (Exception e) {
         log.error("Failed to send data to Node.js for socketId: {}", socketId, e);
      }
   }

   private void setupSocketEventHandlersNonAuthen(Socket socket, String socketId) {
      // Connection events
      socket.on(Socket.EVENT_CONNECT, args -> {
         log.info("[RECEIVED-NODE] Connected to Node.js server for socketId: {}", socketId);
//         connNonAuthen = socket;
      });
      socket.on(Socket.EVENT_DISCONNECT, args -> {
         log.info("[RECEIVED-NODE] Disconnected from Node.js server for socketId: {}", socketId);
         // thực hiện xóa socketID trong danh sách cache tạm!
         connNonAuthen = null;

      });

      socket.on(Socket.EVENT_CONNECT_ERROR, args -> {
         log.error("[RECEIVED-NODE] Connection error to Node.js server for socketId: {} - {}", socketId, args[0]);
      });

      // Chuẩn Nodejs -> Client
      socket.on("RES_MSG", (args) -> {
         if (args == null || args.length == 0) return;
         Object payload = args[0];
         log.debug("[RECEIVED-NODE] Received data response: {} | {}", socketId, payload);
         // Forward response back to client via SocketIO server
         Map<String,Object> body = (payload instanceof org.json.JSONObject jo)
                 ? jo.toMap()
                 : OM.convertValue(payload, new com.fasterxml.jackson.core.type.TypeReference<Map<String,Object>>() {});
         String reqId = String.valueOf(body.get("ClientSeq"));
         if (reqId == null) return;
         Sinks.One<Map<String,Object>> sink = pending.remove(reqId);
         if (sink != null) sink.tryEmitValue(body); // phát kết quả cho Mono
      });
   }


   public Mono<Map<String, Object>> sendToNodeJsNonAuthen(String event,
                                                          Map<String, Object> data,
                                                          Duration timeout) {
      if (event == null || event.isBlank()) {
         return Mono.error(new IllegalArgumentException("event is required"));
      }

      // Clone để tránh side-effect và bảo đảm có ClientSeq
      String reqId = data.get("ClientSeq").toString();
      // Sink chờ phản hồi từ listener "RES_MSG"
      Sinks.One<Map<String, Object>> sink = Sinks.one();
      // Put vào pending trước khi emit
      pending.put(reqId, sink);

      // Đợi có kết nối rồi mới emit
      return creatConnNonAuthen()
              .flatMap(sock -> {
                 try {
                    // GỬI 1 đối số duy nhất: object (không stringify)
                    String os = new ObjectMapper().writeValueAsString(data);
                    sock.emit(event, os);
                    log.debug("Sent event '{}' (reqId={}) to Node.js, payload={}", event, reqId, os);
                    return sink.asMono()
                            .timeout(timeout)
                            .onErrorResume(java.util.concurrent.TimeoutException.class, e ->
                                    Mono.error(new java.util.concurrent.TimeoutException("Timeout waiting for RES_MSG, reqId=" + reqId)));
                 } catch (Throwable t) {
                    pending.remove(reqId);
                    return Mono.error(t);
                 }
              })
              // Dọn pending ở mọi trạng thái (hoàn tất/timeout/error/cancel)
              .doFinally(sig -> pending.remove(reqId));
   }

   /**
    * Send data to client trading WTS/HTS/MTS/MWTS
    * socketId: ID kết nối giữa FOS middleware <-> FOS Trading Online
    * payload: kết quả dữ liệu từ Core trả về
    */
   public void sendToFOSTrading(String socketId, Object payload) {
      try {
         ResponseNode res;
         ObjectMapper objectMapper = new ObjectMapper();
         objectMapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
         if (payload instanceof org.json.JSONObject jo) {
            // deserialize từ JSONObject
            try {
               res = objectMapper.readValue(jo.toString(), ResponseNode.class);
            } catch (JsonProcessingException e) {
               throw new RuntimeException(e);
            }

         } else if (payload instanceof java.util.Map<?, ?> map) {
            // một số lib trả về LinkedHashMap
            res = objectMapper.convertValue(map, ResponseNode.class);
         } else if (payload instanceof String s) {
            // nếu server emit string JSON
            try {
               res = objectMapper.readValue(s, ResponseNode.class);
            } catch (JsonProcessingException e) {
               throw new RuntimeException(e);
            }
         } else {
            log.warn("RES_MSG payload type unsupported: {}", payload.getClass());
            return;
         }

         // TH2: Thực hiện đăng nhập thành công | Đăng xuất thành công
         if(res != null && res.getIsUpdateStore() != null && res.getIsUpdateStore()){
            //TH1: Đăng nhập Thất bại
            if(!res.getCode().contains("000000")){
               log.debug("Login failed: {} | {}", socketId, payload);
               socketIOServer.getClient(UUID.fromString(socketId)).sendEvent("RES_MSG", payload);
               return;
            }
            //TH2: Đăng nhập Thành công
            List<HashMap<String, Object>> list = null;
            Map<String, Object> sessionData = new HashMap<>();
            try {
               list = objectMapper.readValue(res.getData(), new TypeReference<List<HashMap<String, Object>>>(){});
            } catch (JsonProcessingException e) {
               throw new RuntimeException(e);
            }
            // Th2: 1. Thông tin chung sau đăng nhập
            UserFosSession userFosSession = new UserFosSession();
            if(!list.isEmpty()){
               HashMap<String,Object> userInfo = list.get(0);
               String channel = (String) userInfo.get("c0").toString().split("\\|")[1];
               String session_core = (String) userInfo.get("c0").toString().split("\\|")[5];
               userFosSession.setSidMiddle(socketId);
               userFosSession.setTokenWso2(session_core);
               userFosSession.setMdmTp(channel);
               userFosSession.setTokenCore(userInfo.get("c0").toString());
               userFosSession.setUserId(userInfo.get("c1").toString());
            }
            // Th2: 2. Thông tin session login/kết nối
            // Lấy object đầu tiên
            // lấy thông tin session + socketid
            AnonymousSession _session = sessionService().getAnonymousSession(socketId);
            if(_session != null){
               // 2.1: Lấy thông tin thiết bị từ fos:sid
               DeviceInfo deviceInfo = new DeviceInfo();
               deviceInfo.setSocketId(_session.getSocketId());
               deviceInfo.setSessionId(_session.getSessionId());
               deviceInfo.setCreatedAt(_session.getCreatedAt().toString());
               deviceInfo.setExpiresAt(_session.getExpiresAt().toString());
               deviceInfo.setClientIp(_session.getClientIp());
               deviceInfo.setUserAgent(_session.getUserAgent());
               userFosSession.setDeviceInfo(deviceInfo);

               // 2.2: Thông tin wso2
               // 1. API check exist:   id_no | email | phone
               // 2. API tạo tài khoản: id_no | email | phone | họ tên
               // 3. API đăng nhập -> response thì gắn vào

               // 2.3. Thông tin đăng nhập wso2
               // Fake: Gọi API WSO2 lấy accesstoken SSO
               Wso2Info wso2Info = new Wso2Info();
               wso2Info.setAccess_token("bff54fda-fd25-3737-80ee-557282e72352");
               wso2Info.setRefresh_token("5ad5a432-c134-319b-b8e2-c6a523b861e3");
               wso2Info.setScope("email openid profile");
               wso2Info.setToken_type("Bearer");
               wso2Info.setExpires_in(86400);
               wso2Info.setId_token("******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
               userFosSession.setWso2Info(wso2Info);
               userFosSession.setTokenWso2(wso2Info.getAccess_token());
            }
            // Thực hiện tạo fos:uid -> theo fos_id
            // cập nhật authen cho client
            // bật cờ = true khi có accessToken
            userFosSession.setStateConnCore(true);
            UserFosSession userDevice =  sessionService().createAuthenticateUser(userFosSession);

            socketIOServer.getClient(UUID.fromString(socketId)).sendEvent("RES_MSG", payload);
         }else{
            // TH1: Services phản hồi bình thường sẽ emit
            socketIOServer.getClient(UUID.fromString(socketId)).sendEvent("RES_MSG", payload);
         }

      } catch (Exception e) {
         throw new RuntimeException(e);
      }
   }

   /**
    * Upgrade connection when user authenticates
    */
   public void upgradeConnectionForUserDevice(UserFosSession userFosSession, String userId) {
      String socketId = userFosSession.getSidMiddle();

      try {
         Socket socket = socketConnectionsCore.get(socketId);
//         NodeJsConnectionInfo connectionInfo = connectionInfoMap.get(socketId);

//         if (socket == null || connectionInfo == null) {
         if (socket == null ) {
            log.warn("No existing connection found for socketId: {}", socketId);
            return;
         }

         // Update connection info for authenticated user
//         connectionInfo.setUserId(userId);
//         connectionInfo.setAuthenticated(true);
//         connectionInfo.setDeviceId(device.getDeviceId());

         // Send authentication info to Node.js
//         Map<String, Object> authData = Map.of(
//                 "socketId", socketId,
//                 "userId", userId,
//                 "accessToken", device.getAccessToken(),
//                 "deviceId", device.getDeviceId(),
//                 "channel", device.getChannel().name(),
//                 "isPrimary", device.isPrimary(),
//                 "sessionId", device.getSessionId()
//         );

//         socket.emit("authenticate", authData);

         log.info("Upgraded Node.js socket connection for user: {} device: {} channel: {}",
                 userId, userFosSession.getDeviceInfo().getClientIp(), userFosSession.getMdmTp());

      } catch (Exception e) {
         log.error("Failed to upgrade Node.js socket connection for socketId: {}", socketId, e);
      }
   }

   /**
    * Disconnect and cleanup connection
    */
   public void disconnectConnection(String socketId) {
      try {
         Socket socket = socketConnectionsCore.remove(socketId);
//         NodeJsConnectionInfo connectionInfo = connectionInfoMap.remove(socketId);

         if (socket != null) {
            socket.disconnect();
            log.info("Disconnected Node.js socket connection for socketId: {}", socketId);
         }

//         if (connectionInfo != null) {
//            log.debug("Cleaned up connection info for socketId: {}", socketId);
//         }

      } catch (Exception e) {
         log.error("Failed to disconnect Node.js socket for socketId: {}", socketId, e);
      }
   }

   /**
    * Get total active connections
    */
//   public int getActiveConnectionCount() {
//      return (int) socketConnectionsCore.values().stream()
//              .filter(Socket::connected)
//              .count();
//   }


   /**
    * Start connection monitoring
    */
//   public void startConnectionMonitoring() {
//      scheduler.scheduleAtFixedRate(this::monitorConnections, 30, 30, TimeUnit.SECONDS);
//      log.info("Started Node.js connection monitoring");
//   }

   /**
    * Monitor connections and cleanup stale ones
    */
//   private void monitorConnections() {
//      try {
//         long currentTime = System.currentTimeMillis();
//         int cleanedCount = 0;
//
//         for (Map.Entry<String, NodeJsConnectionInfo> entry : connectionInfoMap.entrySet()) {
//            String socketId = entry.getKey();
//            NodeJsConnectionInfo connectionInfo = entry.getValue();
//
//            // Check if connection is stale (no heartbeat for 2 minutes)
////            if (currentTime - connectionInfo.getLastHeartbeat() > 120000) {
////               log.warn("Stale Node.js connection detected for socketId: {}", socketId);
////               disconnectConnection(socketId);
////               cleanedCount++;
////            }
//         }
//
//         if (cleanedCount > 0) {
//            log.info("Cleaned up {} stale Node.js connections", cleanedCount);
//         }
//
//         log.debug("Node.js connection monitoring: {} active connections", getActiveConnectionCount());
//
//      } catch (Exception e) {
//         log.error("Error during Node.js connection monitoring", e);
//      }
//   }

   /**
    * Cleanup all connections on shutdown
    */
   @PreDestroy
   public void cleanup() {
      log.info("Shutting down Node.js Socket Connection Manager");

      // Disconnect all sockets
      socketConnectionsCore.values().forEach(Socket::disconnect);
      socketConnectionsCore.clear();
//      connectionInfoMap.clear();

      // Shutdown scheduler
//      scheduler.shutdown();
//      try {
//         if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
//            scheduler.shutdownNow();
//         }
//      } catch (InterruptedException e) {
//         scheduler.shutdownNow();
         Thread.currentThread().interrupt();
//      }

      log.info("Node.js Socket Connection Manager shutdown complete");
   }


//   // ======================== Thực hiện theo rules ===========//
//   // Socket chung cho No-Authen
//   private volatile Socket sharedSocket;
//   private volatile Mono<Socket> sharedConnectingMono;
//
//   // Socket theo user/thiết bị (sau login)
//   private final Map<String /*userConnId*/, Socket> userSockets = new ConcurrentHashMap<>();
//   private final Map<String /*userConnId*/, Mono<Socket>> userConnecting = new ConcurrentHashMap<>();
//
//   // Pending request chờ RES_MSG (có thể dùng chung nếu ClientSeq là duy nhất)
//   private final Map<String /*ClientSeq*/, Sinks.One<Map<String,Object>>> pending = new ConcurrentHashMap<>();
//
//   // (khuyến nghị) Thêm TTL/lastActivity nếu muốn auto-clean
//   private final Map<String /*userConnId*/, Long /*lastActivityMillis*/> lastSeen = new ConcurrentHashMap<>();
//
//   // quản lý cache
//   private final Map<String /*userId (FOS_ID) */, Map<String /*channel(mdmTp)*/, Object>> listCacheAll = new ConcurrentHashMap<>();
//   // Object socketID, JVM hiện tại



}
