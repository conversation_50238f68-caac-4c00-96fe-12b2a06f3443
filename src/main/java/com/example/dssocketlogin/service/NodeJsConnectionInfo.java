package com.example.dssocketlogin.service;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Information about Socket.IO connection to Node.js server
 * Stored in memory for each active connection
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NodeJsConnectionInfo {

   // Connection identifiers
   private String socketId;
   private String sessionId;
   private String userId;
   private String deviceId;

   // Authentication status
   private boolean isAuthenticated;

   // Connection tracking
   private long createdAt;
}
