package com.example.dssocketlogin.service;

import com.example.dssocketlogin.model.global.RequestTrading;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.socket.client.Socket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.TimeoutException;

/**
 * Thực hiện gọi service FOSGateway
 *
 */

@Service
public class TradingService {
   @Autowired
   private NodeJsSocketConnectionManager nodeJsSocketManager; // đã lazy

   private ObjectMapper OM = new ObjectMapper();

   public Mono<ResponseEntity<Map<String,Object>>> fetchAPIAuthen(@RequestBody RequestTrading requestTrading){
      // thực hiện tạo connection nodejs non-auth
      nodeJsSocketManager.creatConnNonAuthen();
      return null;
   }

   public Mono<ResponseEntity<Map<String,Object>>> fetchAPINonAuthen(@RequestBody RequestTrading requestTrading) throws JsonProcessingException {
//      Mono<Socket> socket = nodeJsSocketManager.creatConnNonAuthen();
//      if(socket == null){
//         return null;
//      }
      // string to Object
      Map<String,Object> map = OM.readValue(requestTrading.getData(), new TypeReference<Map<String,Object>>() {});
      return nodeJsSocketManager
              .sendToNodeJsNonAuthen(requestTrading.getType(),map, Duration.ofSeconds(15))
              .switchIfEmpty(Mono.error(new IllegalStateException("No response from NodeJS"))) // phòng khi empty
              .map(resp -> ResponseEntity.ok().body(resp))
              .onErrorResume(java.util.concurrent.TimeoutException.class,
                      e -> Mono.just(ResponseEntity.status(504).body(Map.of("error", "timeout"))))
              .onErrorResume(ex ->
                      Mono.just(ResponseEntity.status(502).body(Map.of("error", ex.getMessage()))));
   }

   public Mono<ResponseEntity<Map<String,Object>>> fetchAPILogin(@RequestBody RequestTrading requestTrading) throws JsonProcessingException {
      Map<String,Object> map = OM.readValue(requestTrading.getData(), new TypeReference<Map<String,Object>>() {});
      return nodeJsSocketManager
              .sendToNodeJsNonAuthen(requestTrading.getType(),map, Duration.ofSeconds(15))
              .switchIfEmpty(Mono.error(new IllegalStateException("No response from NodeJS"))) // phòng khi empty
              .map(resp -> ResponseEntity.ok().body(resp))
              .onErrorResume(java.util.concurrent.TimeoutException.class,
                      e -> Mono.just(ResponseEntity.status(504).body(Map.of("error", "timeout"))))
              .onErrorResume(ex ->
                      Mono.just(ResponseEntity.status(502).body(Map.of("error", ex.getMessage()))));
   }


}
