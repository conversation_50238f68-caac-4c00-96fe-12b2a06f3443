package com.example.dssocketlogin.service.wso2;

import com.example.dssocketlogin.config.Wso2Config;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class Wso2Service {
   @Qualifier("webClientWso2")
   private final  WebClient externalApiClient;

   @Autowired
   private Wso2Config wso2Config;

   @Value("${wso2.super-user}")
   private String superUser;

   @Value("${wso2.super-password}")
   private String superUserPass;

   // 1. Tạo tài khoản
   public Mono<JsonNode> createAccountWso2(String username, String password,String email) {
      Map<String, Object> emails = new HashMap<>();
      emails.put("value", email);

      MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
      form.add("username", username);
      form.add("password", password);
      form.add("schemas", List.of("urn:ietf:params:scim:schemas:core:2.0:User").toString());
      form.add("emails", List.of(emails).toString());
      Mono<JsonNode> res = externalApiClient.post()
              .uri("/scim2/Users")
              .headers(h -> h.setBasicAuth(superUser, superUserPass))
              .contentType(MediaType.APPLICATION_JSON)
              .body(BodyInserters.fromFormData(form))
              .retrieve()
              .bodyToMono(JsonNode.class);     // trả về JSON
      return res;
   }

   // 2. Đăng nhập
   public Mono<JsonNode> loginWso2(String username, String password) {
      try {
         MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
         form.add("username", username);
         form.add("password", password);
         form.add("grant_type", "password");
         form.add("scope", "openid email profile");

         Mono<JsonNode> res = externalApiClient.post()
                 .uri(wso2Config.getTokenEndpoint())
                 .headers(h -> h.setContentType(MediaType.APPLICATION_FORM_URLENCODED))
                 .headers(h -> h.setBasicAuth(superUser, superUserPass))
                 .body(BodyInserters.fromFormData(form))
                 .retrieve()
                 .bodyToMono(JsonNode.class);     // trả về JSON
         return res;
      } catch (Exception e) {
         log.info("loginWso2", e);
      }
      return null;
   }

   // 3. Kiểm tra token /refresh
   public Mono<JsonNode> checkTokenWso2(String token) {
      try {
         MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
         form.add("token", token);
         Mono<JsonNode> res = externalApiClient.post()
                 .uri(wso2Config.getIntrospectEndpoint())
                 .headers(h -> h.setContentType(MediaType.APPLICATION_FORM_URLENCODED))
                 .headers(h -> h.setBasicAuth(superUser, superUserPass))
                 .body(BodyInserters.fromFormData(form))
                 .retrieve()
                 .bodyToMono(JsonNode.class);     // trả về JSON
         return res;
      } catch (Exception e) {
         log.info("checkTokenWso2", e);
      }
      return null;
   }

   // 4.1 kiểm tra tài khoản tồn tại dựa trên token
   public Mono<JsonNode> checkExistAccountWso2(String token) {
      try {
         Mono<JsonNode> res = externalApiClient.get()
                 .uri("/oauth2/userinfo")
                 .headers(h -> h.setContentType(MediaType.APPLICATION_JSON))
                 .headers(h -> h.setBearerAuth(token))   // <-- Authorization: Bearer <token>
                 .retrieve()
                 .bodyToMono(JsonNode.class);     // trả về JSON
         return res;
      } catch (Exception e) {
         log.info("checkExistAccountWso2", e);
      }
      return null;
   }

   // 4.2 kiểm tra tài khoản tồn tại dựa trên userid
   public Mono<JsonNode> checkExistAccountWso2ByUser(String userId) {
      try {
         MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
         form.add("schemas", List.of("urn:ietf:params:scim:api:messages:2.0:SearchRequest").toString());
         form.add("attributes",List.of("userName, id").toString());
         form.add("domain", "PRIMARY");
         form.add("filter", "userName sw " + userId);

         Mono<JsonNode> res = externalApiClient.post()
                 .uri("/scim2/Users/<USER>")
                 .headers(h -> h.setContentType(MediaType.valueOf("application/scim+json")))
                 .headers(h -> h.setBasicAuth(superUser, superUserPass))
                 .body(BodyInserters.fromFormData(form))
                 .retrieve()
                 .bodyToMono(JsonNode.class);     // trả về JSON
         return res;
      } catch (Exception e) {
         log.info("checkExistAccountWso2", e);
      }
      return null;
   }

   // 5. Xóa tài khoản
   public Mono<JsonNode> deleteAccountWso2(String userId) {
      try {
//         String superUser = "<EMAIL>";
//         String superUserPass = "Ahboish@5oHasei8b";

         Mono<JsonNode> res = externalApiClient.delete()
                 .uri("/scim2/Users/" + userId)
                 .headers(h -> h.setContentType(MediaType.valueOf("application/scim+json")))
                 .headers(h -> h.setBasicAuth(superUser, superUserPass))
                 .retrieve()
                 .bodyToMono(JsonNode.class);     // trả về JSON
         return res;
      } catch (Exception e) {
         log.info("deleteAccountWso2", e);
      }
      return null;
   }

   // 6. LÀM MỚI TOKEN TỪ Refresh token
}
