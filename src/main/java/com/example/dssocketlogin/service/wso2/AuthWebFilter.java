package com.example.dssocketlogin.service.wso2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class AuthWebFilter implements WebFilter {

   @Value("${wso2.client-id}")
   private String clientId;

   @Value("${wso2.client-secret}")
   private String clientSecret;

   @Value("${wso2.super-user}")
   private String superUser;

   @Value("${wso2.super-password}")
   private String superUserPass;

   @Qualifier("webClientWso2")
   private final WebClient wso2; // baseUrl = https://<IS_HOST>:<PORT>

   @Override
   public Mono<Void> filter(ServerWebExchange ex, WebFilterChain chain) {
      String auth = ex.getRequest().getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
      log.info("Auth header: {}", auth);
      if (auth == null || !auth.startsWith("Bearer ")) {
         return chain.filter(ex);
      }
      // kiểm tra xem có accesstoken hợp lệ hay không.
      String token = auth.substring(7);
      MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
      form.add("token", token);
      form.add("grant_type", "access_token");
      return wso2.post()
              .uri("/oauth2/introspect")
              .headers(h -> h.setBasicAuth(superUser, superUserPass))
              .contentType(MediaType.APPLICATION_FORM_URLENCODED)
              .body(BodyInserters.fromFormData(form))
              .retrieve()
              .bodyToMono(new ParameterizedTypeReference<Map<String,Object>>() {})
              .flatMap(body -> {
                 boolean active = Boolean.TRUE.equals(body.get("active"));
                 if (!active) {
                    ex.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                    return ex.getResponse().setComplete();
                 }
                 // OK: tiếp tục
                 return chain.filter(ex);
              });
   }
}
