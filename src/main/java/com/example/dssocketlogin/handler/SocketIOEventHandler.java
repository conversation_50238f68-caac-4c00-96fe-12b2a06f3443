package com.example.dssocketlogin.handler;

import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.listener.ConnectListener;
import com.corundumstudio.socketio.listener.DataListener;
import com.corundumstudio.socketio.listener.DisconnectListener;
import com.example.dssocketlogin.dto.*;
import com.example.dssocketlogin.model.AnonymousSession;
import com.example.dssocketlogin.model.UserFosSession;
//import com.example.dssocketlogin.service.NodeJsIntegrationService;
import com.example.dssocketlogin.service.NodeJsSocketConnectionManager;
import com.example.dssocketlogin.service.SessionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class SocketIOEventHandler {

    private final SocketIOServer socketIOServer;
    private final @Lazy SessionService sessionService;
    private final @Lazy NodeJsSocketConnectionManager nodeJsSocketManager;

    // Map to store Socket.IO connections: socketId -> Website -> Socket Middleware



    @PostConstruct
    public void init() {
        // 1. Tạo một kết nối socket và lưu vào redis với format: fos:sid:{socketId} ->{mdmTp}
        socketIOServer.addConnectListener(onConnected());
        socketIOServer.addDisconnectListener(onDisconnected());
        socketIOServer.addEventListener("REQ_MSG", DataRequest.class, onDataRequest());
        log.info("[INIT] SocketIO Event Handlers initialized");
    }

    // 1. Kết nối vào thì tạo session | Channel(Phải có)
    /*
    * Bước 1: Mở kết nối Anonymous -> Cấp session + socketId + Channel ->
    * -> Tạo session
    * -> KQ: {version: 10 }
    * Bước 2:
    *
    * */
    private ConnectListener onConnected() {
        return client -> {
            try {
                String socketId = client.getSessionId().toString();
                String clientIp = getClientIp(client);
                String userAgent = getUserAgent(client);
                String MdmTp = client.getHandshakeData().getSingleUrlParam("MdmTp");
                String DeviceId = client.getHandshakeData().getSingleUrlParam("DeviceId");
                String DeviceName = client.getHandshakeData().getSingleUrlParam("DeviceName");
                client.set("MdmTp", MdmTp);
                client.set("DeviceId", DeviceId);
                client.set("DeviceName", DeviceName);
                // lưu socket_id như một session
                // Create anonymous session in Redis: fos:sid:{socketId}
                if(nodeJsSocketManager.getSocketConnectionsCore().containsKey(socketId)) {
                    log.info("[CRE] The connection early exist with socketID: {} from IP: {}", socketId, clientIp);
                    return;
                }
                AnonymousSession session = sessionService.createAnonymousSession(socketId, clientIp, userAgent, client.get("MdmTp"));
                // Send connection acknowledgment
                 client.sendEvent("RES_MSG", Map.of(
                    "sessionId", session.getSessionId(),
                    "socketId", socketId,
                    "version", "1.0.0",
                    "timestamp", System.currentTimeMillis(),
                    "message", "Connected successfully"
                ));
                sessionService.getSocketConnectionsMiddle().put(socketId, client);
                log.info("[CRE] Client connected: {} from IP: {} | session: {}", socketId, clientIp, session.getSessionId());
                log.info("[CRE] Device information MdmTp={} | DeviceId={} | DeviceName={}", MdmTp, DeviceId, DeviceName);

            } catch (Exception e) {
                log.error("[CRE] Error handling client connection: {}", e.getMessage(), e);
                client.sendEvent("error", Map.of(
                    "message", "Connection failed",
                    "error", e.getMessage()
                ));
            }
        };
    }

    private DisconnectListener onDisconnected() {
        return client -> {
            try {
                String socketId = client.getSessionId().toString();
                log.info("[PUB] Client disconnected: {}", socketId);
                
                // Clean up session
                sessionService.disconnectBySocketId(socketId);
                
            } catch (Exception e) {
                log.error("[PUB] Error handling client disconnection: {}", e.getMessage(), e);
            }
        };
    }

    private DataListener<DataRequest> onDataRequest() {

        return (client, data, ackSender) -> {
            // Phân 2 loại request

            // 2. authen
            try {
                String socketId = client.getSessionId().toString();
                // kiểm tra sessionID có trong
                // thông tin  SessionID | AppLoginID
                String sessionID = data.getSessionId();
                String appLoginID = data.getAppLoginID();
                String mdmTp = data.getMdmTp();
                UserFosSession user = sessionService.getUserId(appLoginID, mdmTp);
                log.debug("Data request from client: {} for session: {}", socketId, data.getSessionId());

                // accesstoken -> không có thì gọi về node js
                // 1. API non-authen
                if(data.getServiceName() !=null && data.getServiceName().contains("FOSxID02_Login") || sessionID == null ){
                    nodeJsSocketManager.sendToNodeJs(socketId, "REQ_MSG", data.toFlatStringMap());
                    return;
                }
                nodeJsSocketManager.sendToNodeJs(socketId, "REQ_MSG", data.toFlatStringMap());
            } catch (Exception e) {
                log.error("Error handling data request: {}", e.getMessage(), e);
                DataResponse errorResponse = DataResponse.error(
                    data.getMessageId(),
                    data.getSessionId(),
                    "Internal server error"
                );
                client.sendEvent("RES_MSG", errorResponse);
            }
        };
    }

    private String getClientIp(SocketIOClient client) {
        try {
            return client.getRemoteAddress().toString();
        } catch (Exception e) {
            return "unknown";
        }
    }

    private String getUserAgent(SocketIOClient client) {
        try {
            return client.getHandshakeData().getHttpHeaders().get("User-Agent");
        } catch (Exception e) {
            return "unknown";
        }
    }

}
