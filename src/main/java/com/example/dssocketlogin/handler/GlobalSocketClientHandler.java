//package com.example.dssocketlogin.handler;
//
//import io.socket.client.IO;
//import io.socket.client.Socket;
//import io.socket.engineio.client.transports.Polling;
//import io.socket.engineio.client.transports.WebSocket;
//import jakarta.annotation.PostConstruct;
//import jakarta.annotation.PreDestroy;
//import lombok.Getter;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.net.URI;
//import java.util.List;
//import java.util.Map;
//
//@Slf4j
//@Component
//public class GlobalSocketClientHandler {
//
//   @Value("${nodejs.server.socket-url}")       // ví dụ: https://your-node-host:3000
//   private String serverUrl;
//
//   @Getter
//   private Socket socket;
//
//   @PostConstruct
//   public void start() {
//      IO.Options options = IO.Options.builder()
//              // Transports: dùng WebSocket trước, có thể giữ Polling để gửi header tùy nhu cầu
//              .setTransports(new String[]{ WebSocket.NAME, Polling.NAME })  // hoặc chỉ WebSocket.NAME
//              .setPath("/services/")                  // phải khớp server
//              .setReconnection(true)
//              .setReconnectionAttempts(Integer.MAX_VALUE)
//              .setReconnectionDelay(500)
//              .setReconnectionDelayMax(5000)
//              .setTimeout(10_000)
//              // Auth & query: server đọc ở socket.handshake.auth / query
////              .setAuth(Map.of("token", "YOUR_JWT_HERE"))   // khuyến nghị dùng 'auth'
////              .setQuery("deviceId=java-client-01&v=3.1.0") // tuỳ bạn
//              .build();
//
//      // Nếu cần header bổ sung cho handshake (khi còn Polling):
////      options.setExtraHeaders(Map.of("x-client", List.of("java")));
//
//      socket = IO.socket(URI.create(serverUrl), options);
//
//      socket.on(Socket.EVENT_CONNECT, args -> log.info("Connected: {}", socket.id()));
//      socket.on(Socket.EVENT_DISCONNECT, args -> log.warn("Disconnected"));
//      socket.on(Socket.EVENT_CONNECT_ERROR, args -> {
//         log.warn("Connect error: {}", (args != null && args.length > 0) ? args[0] : "unknown");
//         // ví dụ: refresh token rồi reconnect
////         options.auth.put("token", "NEW_JWT");
//         socket.connect();
//      });
//
//      // Lắng nghe event tùy bạn
//      socket.on("hello", args -> log.info("hello: {}", args[0]));
//      socket.on("pong:app", args -> log.info("pong: {}", args[0]));
//
//      socket.connect();
//   }
//
//   public void join(String room) {
//      socket.emit("join", room);
//   }
//
//   public void emit(String event, Object payload) {
//      socket.emit(event, payload);
//   }
//
//   @PreDestroy
//   public void stop() {
//      if (socket != null) socket.close();
//   }
//}
