package com.example.dssocketlogin.helpers;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.LinkedHashMap;
import java.util.Map;

public class UtilHelpers {
   public static final ObjectMapper OM = new ObjectMapper()
           .setSerializationInclusion(JsonInclude.Include.NON_NULL); // bỏ key null (tuỳ chọn)

   public static Map<String,Object> toMap(Object obj) {
      if (obj == null) return Map.of();
      if (obj instanceof Map<?,?> m) return new LinkedHashMap<>( (Map<String,Object>) m );
      return OM.convertValue(obj, new TypeReference<Map<String,Object>>() {});
   }

}
