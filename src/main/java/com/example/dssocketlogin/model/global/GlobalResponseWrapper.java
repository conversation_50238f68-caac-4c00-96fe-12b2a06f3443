//package com.example.dssocketlogin.model.global;
//
//import io.micrometer.common.lang.NonNullApi;
//import org.springframework.core.Ordered;
//import org.springframework.core.ReactiveAdapterRegistry;
//import org.springframework.core.annotation.Order;
//import org.springframework.core.io.Resource;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.http.codec.HttpMessageWriter;
//import org.springframework.http.server.reactive.ServerHttpResponse;
//import org.springframework.stereotype.Component;
//import org.springframework.web.reactive.HandlerResult;
//import org.springframework.web.reactive.accept.RequestedContentTypeResolver;
//import org.springframework.web.reactive.result.method.annotation.ResponseBodyResultHandler;
//import org.springframework.web.server.ServerWebExchange;
//import reactor.core.publisher.Flux;
//import reactor.core.publisher.Mono;
//
//import java.nio.ByteBuffer;
//import java.util.List;
//
//@NonNullApi
//@Component
//@Order(Ordered.HIGHEST_PRECEDENCE)
//public class GlobalResponseWrapper extends ResponseBodyResultHandler {
//
//   public GlobalResponseWrapper(List<HttpMessageWriter<?>> writers,
//                                RequestedContentTypeResolver resolver) {
//      super(writers, resolver, ReactiveAdapterRegistry.getSharedInstance());
//   }
//
//   @Override
//   public boolean supports(HandlerResult result) {
//      // Nếu method đã trả ApiResponse thì không bọc nữa
//      Class<?> rvType = result.getReturnType().toClass();
//      if (ApiResponse.class.isAssignableFrom(rvType)) return false;
//
//      // Bỏ qua một số kiểu thường dùng cho streaming / download
//      if (CharSequence.class.isAssignableFrom(rvType)) return true; // vẫn bọc String
//      if (Resource.class.isAssignableFrom(rvType)) return false;
//      if (byte[].class.isAssignableFrom(rvType)) return false;
//      if (ByteBuffer.class.isAssignableFrom(rvType)) return false;
//
//      return super.supports(result);
//   }
//
//   @Override
//   public Mono<Void> handleResult(ServerWebExchange exchange, HandlerResult result) {
//      // Nếu là SSE (text/event-stream) thì không bọc
//      MediaType ct = exchange.getResponse().getHeaders().getContentType();
//      if (MediaType.TEXT_EVENT_STREAM.equals(ct) || MediaType.APPLICATION_NDJSON.equals(ct)) {
//         return super.handleResult(exchange, result);
//      }
//
//      Object value = result.getReturnValue();
//      ServerHttpResponse resp = exchange.getResponse();
//
//      // 1) ResponseEntity<?>: giữ status/headers, chỉ bọc body
//      if (value instanceof ResponseEntity<?> re) {
//         Object body = re.getBody();
//         if (body instanceof ApiResponse) {
//            return writeBody(re, result.getReturnTypeSource(), exchange);
//         }
//         ApiResponse<Object> wrapped = new ApiResponse<>(
//                 re.getStatusCode().value(),
//                 "success",
//                 body
//         );
//         ResponseEntity<ApiResponse<Object>> out = ResponseEntity
//                 .status(re.getStatusCode())
//                 .headers(re.getHeaders())
//                 .body(wrapped);
//         return writeBody(out, result.getReturnTypeSource(), exchange);
//      }
//
//      // 2) Mono<?>
//      if (value instanceof Mono<?> mono) {
//         Mono<?> mapped = mono.map(b -> (b instanceof ApiResponse)
//                         ? b
//                         : new ApiResponse<>(
//                         resp.getStatusCode() != null ? resp.getStatusCode().value() : 200,
//                         "success",
//                         b
//                 )
//         );
//         return writeBody(mapped, result.getReturnTypeSource(), exchange);
//      }
//
//      // 3) Flux<?>: mặc định bọc từng phần tử (nếu muốn bọc cả list, thu về List trước)
//      if (value instanceof Flux<?> flux) {
//         Flux<?> mapped = flux.map(b -> (b instanceof ApiResponse)
//                         ? b
//                         : new ApiResponse<>(
//                         resp.getStatusCode() != null ? resp.getStatusCode().value() : 200,
//                         "success",
//                         b
//                 )
//         );
//         return writeBody(mapped, result.getReturnTypeSource(), exchange);
//      }
//
//      // 4) Object thường
//      Object body = (value instanceof ApiResponse) ? value
//              : new ApiResponse<>(
//              resp.getStatusCode() != null ? resp.getStatusCode().value() : 200,
//              "success",
//              value
//      );
//      return writeBody(body, result.getReturnTypeSource(), exchange);
//   }
//}
