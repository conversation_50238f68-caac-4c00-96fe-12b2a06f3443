package com.example.dssocketlogin.model.global;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;

import java.util.HashMap;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class RequestTrading {
   private String type;
   private String data;
//   ObjectMapper mapper = new ObjectMapper();
//
//   public Map<String, Object> getObjectData (){
//      if(data == null){
//         return null;
//      }
//
//      Map<String, Object> map = null;
//      try {
//         map = mapper.readValue( data, new TypeReference<Map<String, Object>>() {});
//      } catch (JsonProcessingException e) {
//         return  null;
//      }
//      return map;
//   }

}
