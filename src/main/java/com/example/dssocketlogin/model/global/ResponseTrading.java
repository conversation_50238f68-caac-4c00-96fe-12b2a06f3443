package com.example.dssocketlogin.model.global;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResponseTrading {

   @JsonProperty("TransId")
   private String transID;

   @JsonProperty("ClientSeq")
   private String clientSeq;

   @JsonProperty("Packet")
   private String packet;

   @JsonProperty("Data")
   private String data;

   @JsonProperty("Code")
   private String code;

   @JsonProperty("Message")
   private String message;

   @JsonProperty("Result")
   private String result;
}
