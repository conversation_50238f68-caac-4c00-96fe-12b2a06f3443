package com.example.dssocketlogin.model.global;


import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import reactor.core.publisher.Mono;

@RestControllerAdvice
public class GlobalExceptionHandler {

   @ExceptionHandler(Throwable.class)
   public Mono<ApiResponse<Object>> handleAny(Throwable ex) {
      return Mono.just(new ApiResponse<>(500, ex.getMessage(), null));
   }
}
