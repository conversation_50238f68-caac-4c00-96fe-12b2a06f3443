package com.example.dssocketlogin.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Anonymous session stored in fos:sid:{socketId}
 * Contains minimal information for unauthenticated connections
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnonymousSession {
    
    private String socketId;
    private String sessionId;
    private String channel;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAccessedAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastHeartbeat;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;
    
    private String clientIp;
    private String userAgent;
    private Map<String, Object> metadata;
    
    // Connection tracking
    private boolean isActive;
    private int reconnectCount;
    
    public static AnonymousSession create(String socketId, String clientIp, String userAgent, String channel) {
        LocalDateTime now = LocalDateTime.now();
//        Channel channel = Channel.fromUserAgent(userAgent);
        
        return AnonymousSession.builder()
                .socketId(socketId)
                .sessionId(generateSessionId())
                .channel(channel)
                .createdAt(now)
                .lastAccessedAt(now)
                .lastHeartbeat(now)
                .expiresAt(now.plusHours(1)) // 1 hour default
                .clientIp(clientIp)
                .userAgent(userAgent)
                .isActive(true)
                .reconnectCount(0)
                .build();
    }
    
    public void updateHeartbeat() {
        this.lastHeartbeat = LocalDateTime.now();
        this.lastAccessedAt = LocalDateTime.now();
    }
    
    public void updateLastAccessed() {
        this.lastAccessedAt = LocalDateTime.now();
    }
    
    public void disconnect() {
        this.isActive = false;
        this.lastAccessedAt = LocalDateTime.now();
    }
    
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }
    
    public void incrementReconnect() {
        this.reconnectCount++;
    }
    
    private static String generateSessionId() {
        return "anon_" + System.currentTimeMillis() + "_" + 
               Long.toHexString(Double.doubleToLongBits(Math.random()));
    }
}
