package com.example.dssocketlogin.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFosSessionAuthen {

   private String userId;
   private Map<String, UserFosSession> devicesByChannel;

   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private LocalDateTime createdAt;

   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private LocalDateTime lastUpdatedAt;

   // User preferences
   @Builder.Default
   private int maxDevicesPerChannel = 3; // Maximum devices per channel

   @Builder.Default
   private boolean allowMultipleLogins = true;

   @Builder.Default
   private String primaryChannel = "02"; // -> mặc định kênh website

   // Statistics
   private int totalDevices;     //-> tổng số kết nối
   private int activeDevices;    //-> thiết bị đang hoạt động



//   // Danh sách:
//
//   void addUserChannel(UserFosSession userFosSession){
//
//   }
//   boolean removeUserChannel(UserFosSession userFosSession){
//      return false;
//   }
//   //-> Lấy thông tin kết nối theo socketId (theo fos_id)
//   private Optional<UserFosSession> getUserConn(String socketId){
//      return Optional.empty();
//   }
//   //-> Lấy thông tin kết nối theo kênh (theo fos_id)
//   private List<UserFosSession> getUserConn(String mdmtp){
//      return null;
//   }
//
//   //			-> Lấy số lượng tổng kết nối theo kênh (theo fos_id)
//   private  Map<String, Integer> getChannelStatistics (){
//      return null;
//   }

}
