package com.example.dssocketlogin.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Wso2Info {
   private String access_token;
   private String refresh_token;
   private String scope;
   private String token_type;
   private Integer expires_in;
   private String id_token;
}
