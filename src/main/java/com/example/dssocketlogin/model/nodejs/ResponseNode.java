package com.example.dssocketlogin.model.nodejs;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResponseNode {
   @JsonAlias({"ClientSeq", "clientSeq"})
   private String ClientSeq;
   @JsonAlias({"Code", "code"})
   private String Code;
   @JsonAlias({"Data", "data"})
   private String Data;
   @JsonAlias({"Packet", "packet"})
   private String Packet;
   @JsonAlias({"Result", "result"})
   private String Result;
   @JsonAlias({"TransId", "transId"})
   private String TransId;
   @JsonAlias({"IsUpdateStore", "isUpdateStore"})
   private Boolean isUpdateStore;
}
