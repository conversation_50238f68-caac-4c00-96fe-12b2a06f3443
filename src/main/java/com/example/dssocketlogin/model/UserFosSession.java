package com.example.dssocketlogin.model;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;



/**
 * Thông tin thiết bị được lưu theo <PERSON> hàng
 * Part of fos:uid:{userId} structure
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserFosSession {

   private String sidMiddle;      // sidMiddle
   private String sidCore;      // sidCore
   private String userId;    // userId tương đương FOS_ID
   private String mdmTp;     // mdmTp  kênh đăng nhập: WTS - MWTS - HTS - MTS(13,14)

   private String tokenCore;      // tokenCore = sessionID -> trường c1 sau login
   private String tokenWso2;    // tokenWso2
   private DeviceInfo deviceInfo;
   private Wso2Info wso2Info;

   // trạng thái kết nối core
   private Boolean stateConnCore;

   // trạng thái kết nối giữ client + middleware (new)
   private Boolean stateConnMW;

}

