package com.example.dssocketlogin.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceInfo {
   private String socketId;
   private String sessionId;
   private String createdAt;
   private String expiresAt;
   private String clientIp;
   private String userAgent;
}
