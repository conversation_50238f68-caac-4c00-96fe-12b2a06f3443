package com.example.dssocketlogin.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSession {
    
    private String sessionId;
    private String userId;
    private String socketId;
    private String accessToken;
    private String refreshToken;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAccessedAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;
    
    private String clientIp;
    private String userAgent;
    private Map<String, Object> metadata;
    
    // Connection info
    private String nodeJsConnectionId;
    private boolean isNodeJsConnected;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastHeartbeat;
    
    public static UserSession create(String socketId, String clientIp, String userAgent) {
        LocalDateTime now = LocalDateTime.now();
        // tạo mới session
        return UserSession.builder()
                .sessionId(UUID.randomUUID().toString())
                .socketId(socketId)
                .createdAt(now)
                .lastAccessedAt(now)
                .lastHeartbeat(now)
                .clientIp(clientIp)
                .userAgent(userAgent)
                .isNodeJsConnected(false)
                .build();
    }
    
    public void updateLastAccessed() {
        this.lastAccessedAt = LocalDateTime.now();
    }
    
    public void updateHeartbeat() {
        this.lastHeartbeat = LocalDateTime.now();
        this.lastAccessedAt = LocalDateTime.now();
    }
    
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
    

    public void authenticate(String userId, String accessToken, String refreshToken, int timeoutSeconds) {
        this.userId = userId;
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.expiresAt = LocalDateTime.now().plusSeconds(timeoutSeconds);
        updateLastAccessed();
    }
    
    public void disconnect() {
        this.isNodeJsConnected = false;
        updateLastAccessed();
    }
}
