package com.example.dssocketlogin.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.web.reactive.accept.RequestedContentTypeResolver;
import org.springframework.web.reactive.accept.RequestedContentTypeResolverBuilder;
import org.springframework.web.reactive.config.CorsRegistry;
import org.springframework.web.reactive.config.EnableWebFlux;
import org.springframework.web.reactive.config.ResourceHandlerRegistry;
import org.springframework.web.reactive.config.WebFluxConfigurer;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

import static org.springframework.web.reactive.function.server.RequestPredicates.GET;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;
import static org.springframework.web.reactive.function.server.ServerResponse.ok;

@Slf4j
@Configuration
@EnableWebFluxSecurity
public class WebFluxConfig implements WebFluxConfigurer {

    @Value("${nodejs.server.connection-timeout}")
    private int connectionTimeout;

    @Value("${nodejs.server.read-timeout}")
    private int readTimeout;

    @Value("${nodejs.server.write-timeout}")
    private int writeTimeout;

    @Value("${wso2.base-url}")
    private String hostname;



    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }

    @Bean
    public RequestedContentTypeResolver requestedContentTypeResolver() {
        return new RequestedContentTypeResolverBuilder().build();
    }


    @Bean
    public ConnectionProvider connectionProvider() {
        return ConnectionProvider.builder("custom")
                .maxConnections(100)
                .maxIdleTime(Duration.ofSeconds(20))
                .maxLifeTime(Duration.ofSeconds(60))
                .pendingAcquireTimeout(Duration.ofSeconds(60))
                .evictInBackground(Duration.ofSeconds(120))
                .build();
    }

    @Bean
    public HttpClient httpClient(ConnectionProvider connectionProvider) {
        return HttpClient.create(connectionProvider)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectionTimeout)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.TCP_NODELAY, true)
                .doOnConnected(conn ->
                        conn.addHandlerLast(new ReadTimeoutHandler(readTimeout, TimeUnit.MILLISECONDS))
                            .addHandlerLast(new WriteTimeoutHandler(writeTimeout, TimeUnit.MILLISECONDS)))
                .compress(true)
                .keepAlive(true);
    }



    @Bean("webClientWso2")
    @Primary
    public WebClient webClientWso2(HttpClient httpClient) {
        return WebClient.builder()
                .baseUrl(hostname)
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .codecs(configurer -> {
                    configurer.defaultCodecs().maxInMemorySize(1024 * 1024 * 10); // 10MB
                })
                .build();
    }

    @Bean
    public WebClient nodeJsWebClient(HttpClient httpClient, @Value("${nodejs.server.socket-url}") String nodeJsUrl) {
        return WebClient.builder()
                .baseUrl(nodeJsUrl)
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .codecs(configurer -> {
                    configurer.defaultCodecs().maxInMemorySize(1024 * 1024); // 1MB
                })
                .build();
    }

    @Bean
    public RouterFunction<ServerResponse> staticResourceRouter() {
        return route(GET("/test-client.html"), request -> {
            try {
                ClassPathResource resource = new ClassPathResource("static/test-client.html");
                if (resource.exists()) {
                    return ok().contentType(MediaType.TEXT_HTML)
                            .bodyValue(resource);
                } else {
                    log.warn("test-client.html not found in static resources");
                    return ServerResponse.notFound().build();
                }
            } catch (Exception e) {
                log.error("Error serving test-client.html", e);
                return ServerResponse.notFound().build();
            }
        }).andRoute(GET("/"), request -> {
            try {
                ClassPathResource resource = new ClassPathResource("static/test-client.html");
                if (resource.exists()) {
                    return ok().contentType(MediaType.TEXT_HTML)
                            .bodyValue(resource);
                } else {
                    return ServerResponse.notFound().build();
                }
            } catch (Exception e) {
                log.error("Error serving index page", e);
                return ServerResponse.notFound().build();
            }
        });
    }

    @Bean
    SecurityWebFilterChain security(ServerHttpSecurity http) {
        return http
                .csrf(ServerHttpSecurity.CsrfSpec::disable)   // or: csrf(csrf -> csrf.disable())
                .build();
    }
}
