package com.example.dssocketlogin.config;

import com.corundumstudio.socketio.Configuration;
import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.Transport;
import com.corundumstudio.socketio.protocol.JacksonJsonSupport;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.datatype.jsonorg.JsonOrgModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import jakarta.annotation.PreDestroy;

@Slf4j
@org.springframework.context.annotation.Configuration
public class SocketIOConfig {

    @Value("${socketio.server.hostname}")
    private String hostname;

    @Value("${socketio.server.port}")
    private int port;

    @Value("${socketio.server.worker-threads}")
    private int workerThreads;

    @Value("${socketio.server.boss-threads}")
    private int bossThreads;

    @Value("${socketio.server.allow-custom-requests}")
    private boolean allowCustomRequests;

    @Value("${socketio.server.upgrade-timeout}")
    private int upgradeTimeout;

    @Value("${socketio.server.ping-timeout}")
    private int pingTimeout;

    @Value("${socketio.server.ping-interval}")
    private int pingInterval;

    @Value("${socketio.server.max-frame-payload-length}")
    private int maxFramePayloadLength;

    @Value("${socketio.server.max-http-content-length}")
    private int maxHttpContentLength;

    @Value("${socketio.server.cors.allowed-origins}")
    private String allowedOrigins;

    @Value("${socketio.server.cors.allowed-methods}")
    private String allowedMethods;

    @Value("${socketio.server.cors.allowed-headers}")
    private String allowedHeaders;

    @Value("${socketio.server.cors.allow-credentials}")
    private boolean allowCredentials;

    private SocketIOServer server;

    @Bean
    @Primary
    public SocketIOServer socketIOServer() {
        Configuration config = new Configuration();
        // cho phép truyền JSON
        var json = new JacksonJsonSupport() {{ objectMapper.registerModule(new JsonOrgModule()); }};
        config.setJsonSupport(json);

        // Basic server configuration
        config.setHostname(hostname);
        config.setPort(port);
        config.setWorkerThreads(workerThreads);
        config.setBossThreads(bossThreads);
        
        // Transport configuration
        config.setTransports(Transport.WEBSOCKET, Transport.POLLING);
        config.setAllowCustomRequests(allowCustomRequests);
        config.setUpgradeTimeout(upgradeTimeout);
        config.setPingTimeout(pingTimeout);
        config.setPingInterval(pingInterval);
        
        // Frame and content size limits
        config.setMaxFramePayloadLength(maxFramePayloadLength);
        config.setMaxHttpContentLength(maxHttpContentLength);
        
        // CORS configuration
        config.setOrigin(allowedOrigins);
        
        // Additional Netty configuration for performance
        // Note: SocketConfig configuration may vary by netty-socketio version

        // Enable authentication if needed
        config.setAuthorizationListener(data -> {
            // Custom authorization logic can be added here
            String mdmTp = data.getSingleUrlParam("MdmTp");

            log.debug("Authorization request from: {} | channel: {} ", data.getAddress(), mdmTp);
            return com.corundumstudio.socketio.AuthorizationResult.SUCCESSFUL_AUTHORIZATION;
        });
        
        server = new SocketIOServer(config);
        
        log.info("SocketIO Server configured on {}:{} with {} worker threads", 
                hostname, port, workerThreads);
        
        return server;
    }

    @Bean
    public SocketIOServerRunner socketIOServerRunner(SocketIOServer server) {
        return new SocketIOServerRunner(server);
    }

    @PreDestroy
    public void stopSocketIOServer() {
        if (server != null) {
            log.info("Stopping SocketIO Server...");
            server.stop();
        }
    }

    /**
     * Runner class to start the SocketIO server
     */
    public static class SocketIOServerRunner {
        private final SocketIOServer server;

        public SocketIOServerRunner(SocketIOServer server) {
            this.server = server;
            startServer();
        }

        private void startServer() {
            try {
                server.start();
                log.info("SocketIO Server started successfully on port {}", server.getConfiguration().getPort());
            } catch (Exception e) {
                log.error("Failed to start SocketIO Server", e);
                throw new RuntimeException("Failed to start SocketIO Server", e);
            }
        }

        public void stopServer() {
            if (server != null) {
                server.stop();
                log.info("SocketIO Server stopped");
            }
        }
    }
}
