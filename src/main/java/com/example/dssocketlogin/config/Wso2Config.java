package com.example.dssocketlogin.config;

import lombok.*;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Value;

@Configuration
@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class Wso2Config {

   @Value("${wso2.base-url}")
   private String hostname;

   @Value("${wso2.super-user}")
   private String superUser;

   @Value("${wso2.super-password}")
   private String superPassword;

   @Value("${wso2.client-id}")
   private String clientId;

   @Value("${wso2.client-secret}")
   private String clientSecret;

   @Value("${wso2.token-endpoint}")
   private String tokenEndpoint;

   @Value("${wso2.introspect-endpoint}")
   private String introspectEndpoint;

   @Value("${wso2.scope}")
   private String wso2Scope;

   @Value("${wso2.timeout-ms}")
   private String timeoutWso2;



}
