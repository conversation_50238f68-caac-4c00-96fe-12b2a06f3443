package com.example.dssocketlogin.config;

import com.example.dssocketlogin.model.AnonymousSession;
import com.example.dssocketlogin.model.UserFosSession;
import com.example.dssocketlogin.model.UserSession;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.SocketOptions;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.Map;

@Slf4j
@Configuration
public class RedisConfig {

    @Value("${spring.data.redis.host}")
    private String redisHost;

    @Value("${spring.data.redis.port}")
    private int redisPort;

    @Value("${spring.data.redis.database}")
    private int database;

    @Value("${spring.data.redis.timeout}")
    private Duration timeout;

    @Bean
    @Primary
    public LettuceConnectionFactory redisConnectionFactory() {
        // Redis Standalone Configuration
        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
        redisConfig.setHostName(redisHost);
        redisConfig.setPort(redisPort);
        redisConfig.setDatabase(database);

        // Lettuce Client Configuration
        SocketOptions socketOptions = SocketOptions.builder()
                .connectTimeout(timeout)
                .build();

        ClientOptions clientOptions = ClientOptions.builder()
                .socketOptions(socketOptions)
                .build();

        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                .clientOptions(clientOptions)
                .commandTimeout(timeout)
                .build();

        LettuceConnectionFactory factory = new LettuceConnectionFactory(redisConfig, clientConfig);
        factory.setValidateConnection(true);
        
        log.info("Redis connection factory configured for {}:{}", redisHost, redisPort);
        return factory;
    }

    @Bean
    public ObjectMapper redisObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.findAndRegisterModules();
        return mapper;
    }

    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // String serializer for keys
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);

        // JSON serializer for values
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer(redisObjectMapper());
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);

        template.setDefaultSerializer(jsonSerializer);
        template.afterPropertiesSet();

        log.info("RedisTemplate configured with JSON serialization");
        return template;
    }

    @Bean
    public RedisTemplate<String, UserSession> sessionRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, UserSession> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // String serializer for keys
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);

        // JSON serializer for UserSession values
        Jackson2JsonRedisSerializer<UserSession> sessionSerializer = new Jackson2JsonRedisSerializer<>(redisObjectMapper(), UserSession.class);
        template.setValueSerializer(sessionSerializer);
        template.setHashValueSerializer(sessionSerializer);

        template.setDefaultSerializer(sessionSerializer);
        template.afterPropertiesSet();

        log.info("Session RedisTemplate configured");
        return template;
    }

    @Bean
    public ReactiveRedisTemplate<String, Object> reactiveRedisTemplate(LettuceConnectionFactory connectionFactory) {
        org.springframework.data.redis.serializer.RedisSerializationContext<String, Object> context =
                org.springframework.data.redis.serializer.RedisSerializationContext
                        .<String, Object>newSerializationContext()
                        .key(StringRedisSerializer.UTF_8)
                        .value(new GenericJackson2JsonRedisSerializer(redisObjectMapper()))
                        .hashKey(StringRedisSerializer.UTF_8)
                        .hashValue(new GenericJackson2JsonRedisSerializer(redisObjectMapper()))
                        .build();

        ReactiveRedisTemplate<String, Object> template = new ReactiveRedisTemplate<String, Object>(connectionFactory, context);

        log.info("Reactive RedisTemplate configured");
        return template;
    }

    @Bean
    public ReactiveRedisTemplate<String, UserSession> reactiveSessionRedisTemplate(LettuceConnectionFactory connectionFactory) {
        Jackson2JsonRedisSerializer<UserSession> sessionSerializer = new Jackson2JsonRedisSerializer<>(redisObjectMapper(), UserSession.class);

        org.springframework.data.redis.serializer.RedisSerializationContext<String, UserSession> context =
                org.springframework.data.redis.serializer.RedisSerializationContext
                        .<String, UserSession>newSerializationContext()
                        .key(StringRedisSerializer.UTF_8)
                        .value(sessionSerializer)
                        .hashKey(StringRedisSerializer.UTF_8)
                        .hashValue(sessionSerializer)
                        .build();

        ReactiveRedisTemplate<String, UserSession> template = new ReactiveRedisTemplate<String, UserSession>(connectionFactory, context);

        log.info("Reactive Session RedisTemplate configured");
        return template;
    }

    // ==================== NEW REDIS TEMPLATES FOR REDESIGNED ARCHITECTURE ====================

    @Bean
    public RedisTemplate<String, AnonymousSession> anonymousSessionRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, AnonymousSession> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // String serializer for keys
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);

        // JSON serializer for AnonymousSession values
        Jackson2JsonRedisSerializer<AnonymousSession> sessionSerializer =
                new Jackson2JsonRedisSerializer<>(redisObjectMapper(), AnonymousSession.class);
        template.setValueSerializer(sessionSerializer);
        template.setHashValueSerializer(sessionSerializer);

        template.setDefaultSerializer(sessionSerializer);
        template.afterPropertiesSet();

        log.info("Anonymous Session RedisTemplate configured");
        return template;
    }

    @Bean
    public ReactiveRedisTemplate<String, AnonymousSession> reactiveAnonymousSessionRedisTemplate(LettuceConnectionFactory connectionFactory) {
        Jackson2JsonRedisSerializer<AnonymousSession> sessionSerializer =
                new Jackson2JsonRedisSerializer<>(redisObjectMapper(), AnonymousSession.class);

        org.springframework.data.redis.serializer.RedisSerializationContext<String, AnonymousSession> context =
                org.springframework.data.redis.serializer.RedisSerializationContext
                        .<String, AnonymousSession>newSerializationContext()
                        .key(StringRedisSerializer.UTF_8)
                        .value(sessionSerializer)
                        .hashKey(StringRedisSerializer.UTF_8)
                        .hashValue(sessionSerializer)
                        .build();

        ReactiveRedisTemplate<String, AnonymousSession> template =
                new ReactiveRedisTemplate<String, AnonymousSession>(connectionFactory, context);

        log.info("Reactive Anonymous Session RedisTemplate configured");
        return template;
    }

//    @Bean
//    public RedisTemplate<String, UserDeviceList> userDeviceRedisTemplate(RedisConnectionFactory connectionFactory) {
//        RedisTemplate<String, UserDeviceList> template = new RedisTemplate<>();
//        template.setConnectionFactory(connectionFactory);
//
//        // String serializer for keys
//        StringRedisSerializer stringSerializer = new StringRedisSerializer();
//        template.setKeySerializer(stringSerializer);
//        template.setHashKeySerializer(stringSerializer);
//
//        // JSON serializer for UserDeviceList values
//        Jackson2JsonRedisSerializer<UserDeviceList> deviceListSerializer =
//                new Jackson2JsonRedisSerializer<>(redisObjectMapper(), UserDeviceList.class);
//        template.setValueSerializer(deviceListSerializer);
//        template.setHashValueSerializer(deviceListSerializer);
//
//        template.setDefaultSerializer(deviceListSerializer);
//        template.afterPropertiesSet();
//
//        log.info("User Device List RedisTemplate configured");
//        return template;
//    }
//
//    @Bean
//    public ReactiveRedisTemplate<String, UserDeviceList> reactiveUserDeviceRedisTemplate(LettuceConnectionFactory connectionFactory) {
//        Jackson2JsonRedisSerializer<UserDeviceList> deviceListSerializer =
//                new Jackson2JsonRedisSerializer<>(redisObjectMapper(), UserDeviceList.class);
//
//        org.springframework.data.redis.serializer.RedisSerializationContext<String, UserDeviceList> context =
//                org.springframework.data.redis.serializer.RedisSerializationContext
//                        .<String, UserDeviceList>newSerializationContext()
//                        .key(StringRedisSerializer.UTF_8)
//                        .value(deviceListSerializer)
//                        .hashKey(StringRedisSerializer.UTF_8)
//                        .hashValue(deviceListSerializer)
//                        .build();
//
//        ReactiveRedisTemplate<String, UserDeviceList> template =
//                new ReactiveRedisTemplate<String, UserDeviceList>(connectionFactory, context);
//
//        log.info("Reactive User Device List RedisTemplate configured");
//        return template;
//    }

    // ================== NhacVB Thêm mới =======================
    @Bean
    public RedisTemplate <String, Map<String, UserFosSession>> userChannelRedisTemplate(LettuceConnectionFactory connectionFactory){
        RedisTemplate<String, Map<String, UserFosSession>> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // String serializer for keys
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);

        // Value/Hash-value generic-safe
        var generic = new GenericJackson2JsonRedisSerializer();
        template.setValueSerializer(generic);
        template.setHashValueSerializer(generic);

        template.afterPropertiesSet();
        log.info("User Conn List RedisTemplate configured");
        return template;
    }
        //listUserFosSessionRedisTemplate

    @Bean
    public RedisTemplate<String, UserFosSession> listUserFosSessionRedisTemplate(LettuceConnectionFactory connectionFactory) {
                RedisTemplate<String, UserFosSession> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // String serializer for keys
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);

        // JSON serializer for UserFosSession values
        Jackson2JsonRedisSerializer<UserFosSession> userListSerializer =
                new Jackson2JsonRedisSerializer<>(redisObjectMapper(), UserFosSession.class);
        template.setValueSerializer(userListSerializer);
        template.setHashValueSerializer(userListSerializer);

        template.setDefaultSerializer(userListSerializer);
        template.afterPropertiesSet();

        log.info("User Device List RedisTemplate configured");
        return template;
    }
//
//    @Bean
//    public RedissonClient redisson(@Value("${redis.url}") String url) {
//        Config cfg = new Config();
//        cfg.useSingleServer().setAddress(redisHost+":"+redisPort);
//        return Redisson.create(cfg);
//    }

}
