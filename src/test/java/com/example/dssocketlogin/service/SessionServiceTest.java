//package com.example.dssocketlogin.service;
//
//import com.example.dssocketlogin.model.SessionStatus;
//import com.example.dssocketlogin.model.UserSession;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.data.redis.core.ReactiveRedisTemplate;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.core.ValueOperations;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.time.LocalDateTime;
//import java.util.concurrent.TimeUnit;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//class SessionServiceTest {
//
//    @Mock
//    private RedisTemplate<String, UserSession> sessionRedisTemplate;
//
//    @Mock
//    private ReactiveRedisTemplate<String, UserSession> reactiveSessionRedisTemplate;
//
//
//    @Mock
//    private ValueOperations<String, UserSession> valueOperations;
//
//    @Mock
//    private ValueOperations<String, String> stringValueOperations;
//
//    private SessionService sessionService;
//
//    @BeforeEach
//    void setUp() {
//        sessionService = new SessionService(sessionRedisTemplate, reactiveSessionRedisTemplate);
//
//        // Set test values using reflection
//        ReflectionTestUtils.setField(sessionService, "defaultTimeoutSeconds", 1800);
//        ReflectionTestUtils.setField(sessionService, "keyPrefix", "ds:session:");
//        ReflectionTestUtils.setField(sessionService, "maxSessionsPerUser", 5);
//
//        when(sessionRedisTemplate.opsForValue()).thenReturn(valueOperations);
//    }
//
//    @Test
//    void createSession_ShouldCreateAndStoreSession() {
//        // Given
//        String socketId = "socket123";
//        String clientIp = "***********";
//        String userAgent = "Mozilla/5.0";
//
//        // When
//        UserSession result = sessionService.createSession(socketId, clientIp, userAgent);
//
//        // Then
//        assertNotNull(result);
//        assertEquals(socketId, result.getSocketId());
//        assertEquals(clientIp, result.getClientIp());
//        assertEquals(userAgent, result.getUserAgent());
//        assertEquals(SessionStatus.CONNECTED, result.getStatus());
//        assertNotNull(result.getSessionId());
//        assertNotNull(result.getCreatedAt());
//        assertNotNull(result.getLastAccessedAt());
//        assertNotNull(result.getLastHeartbeat());
//
//        // Verify Redis operations
//        verify(valueOperations).set(eq("ds:session:" + result.getSessionId()), eq(result), eq(1800), eq(TimeUnit.SECONDS));
//        verify(valueOperations).set(eq("ds:session:socket:" + socketId), eq(result.getSessionId()), eq(1800), eq(TimeUnit.SECONDS));
//    }
//
//    @Test
//    void getSession_ShouldReturnSession_WhenExists() {
//        // Given
//        String sessionId = "session123";
//        UserSession mockSession = UserSession.builder()
//                .sessionId(sessionId)
//                .socketId("socket123")
//                .status(SessionStatus.CONNECTED)
//                .createdAt(LocalDateTime.now())
//                .lastAccessedAt(LocalDateTime.now())
//                .expiresAt(LocalDateTime.now().plusMinutes(30))
//                .build();
//
//        when(valueOperations.get("ds:session:" + sessionId)).thenReturn(mockSession);
//
//        // When
//        UserSession result = sessionService.getSession(sessionId);
//
//        // Then
//        assertNotNull(result);
//        assertEquals(sessionId, result.getSessionId());
//        assertEquals("socket123", result.getSocketId());
//        assertEquals(SessionStatus.CONNECTED, result.getStatus());
//    }
//
//    @Test
//    void getSession_ShouldReturnNull_WhenNotExists() {
//        // Given
//        String sessionId = "nonexistent";
//        when(valueOperations.get("ds:session:" + sessionId)).thenReturn(null);
//
//        // When
//        UserSession result = sessionService.getSession(sessionId);
//
//        // Then
//        assertNull(result);
//    }
//
//    @Test
//    void getSession_ShouldReturnNull_WhenExpired() {
//        // Given
//        String sessionId = "expired123";
//        UserSession expiredSession = UserSession.builder()
//                .sessionId(sessionId)
//                .socketId("socket123")
//                .status(SessionStatus.CONNECTED)
//                .createdAt(LocalDateTime.now().minusHours(2))
//                .lastAccessedAt(LocalDateTime.now().minusHours(1))
//                .expiresAt(LocalDateTime.now().minusMinutes(30)) // Expired 30 minutes ago
//                .build();
//
//        when(valueOperations.get("ds:session:" + sessionId)).thenReturn(expiredSession);
//
//        // When
//        UserSession result = sessionService.getSession(sessionId);
//
//        // Then
//        assertNull(result);
//
//        // Verify session was deleted
//        verify(sessionRedisTemplate).delete("ds:session:" + sessionId);
//    }
//
//    @Test
//    void validateSession_ShouldReturnSession_WhenValid() {
//        // Given
//        String sessionId = "session123";
//        String accessToken = "token123";
//        UserSession mockSession = UserSession.builder()
//                .sessionId(sessionId)
//                .socketId("socket123")
//                .status(SessionStatus.AUTHENTICATED)
//                .accessToken(accessToken)
//                .createdAt(LocalDateTime.now())
//                .lastAccessedAt(LocalDateTime.now())
//                .expiresAt(LocalDateTime.now().plusMinutes(30))
//                .build();
//
//        when(valueOperations.get("ds:session:" + sessionId)).thenReturn(mockSession);
//
//        // When
//        UserSession result = sessionService.validateSession(sessionId, accessToken);
//
//        // Then
//        assertNotNull(result);
//        assertEquals(sessionId, result.getSessionId());
//        assertEquals(accessToken, result.getAccessToken());
//
//        // Verify session was updated with new last accessed time
//        verify(valueOperations, times(2)).set(eq("ds:session:" + sessionId), any(UserSession.class), eq(1800), eq(TimeUnit.SECONDS));
//    }
//
//    @Test
//    void validateSession_ShouldReturnNull_WhenTokenMismatch() {
//        // Given
//        String sessionId = "session123";
//        String accessToken = "token123";
//        String wrongToken = "wrongtoken";
//        UserSession mockSession = UserSession.builder()
//                .sessionId(sessionId)
//                .socketId("socket123")
//                .status(SessionStatus.AUTHENTICATED)
//                .accessToken(accessToken)
//                .createdAt(LocalDateTime.now())
//                .lastAccessedAt(LocalDateTime.now())
//                .expiresAt(LocalDateTime.now().plusMinutes(30))
//                .build();
//
//        when(valueOperations.get("ds:session:" + sessionId)).thenReturn(mockSession);
//
//        // When
//        UserSession result = sessionService.validateSession(sessionId, wrongToken);
//
//        // Then
//        assertNull(result);
//    }
//
//    @Test
//    void validateSession_ShouldReturnNull_WhenNotAuthenticated() {
//        // Given
//        String sessionId = "session123";
//        String accessToken = "token123";
//        UserSession mockSession = UserSession.builder()
//                .sessionId(sessionId)
//                .socketId("socket123")
//                .status(SessionStatus.CONNECTED) // Not authenticated
//                .createdAt(LocalDateTime.now())
//                .lastAccessedAt(LocalDateTime.now())
//                .expiresAt(LocalDateTime.now().plusMinutes(30))
//                .build();
//
//        when(valueOperations.get("ds:session:" + sessionId)).thenReturn(mockSession);
//
//        // When
//        UserSession result = sessionService.validateSession(sessionId, accessToken);
//
//        // Then
//        assertNull(result);
//    }
//
//    @Test
//    void authenticateSession_ShouldUpdateSession() {
//        // Given
//        String sessionId = "session123";
//        String userId = "user123";
//        String accessToken = "token123";
//        String refreshToken = "refresh123";
//
//        UserSession mockSession = UserSession.builder()
//                .sessionId(sessionId)
//                .socketId("socket123")
//                .status(SessionStatus.CONNECTED)
//                .createdAt(LocalDateTime.now())
//                .lastAccessedAt(LocalDateTime.now())
//                .build();
//
//        when(valueOperations.get("ds:session:" + sessionId)).thenReturn(mockSession);
//
//        // When
//        sessionService.authenticateSession(sessionId, userId, accessToken, refreshToken);
//
//        // Then
//        verify(valueOperations, times(2)).set(eq("ds:session:" + sessionId), any(UserSession.class), eq(1800), eq(TimeUnit.SECONDS));
//    }
//
//    @Test
//    void updateHeartbeat_ShouldUpdateSession() {
//        // Given
//        String sessionId = "session123";
//        UserSession mockSession = UserSession.builder()
//                .sessionId(sessionId)
//                .socketId("socket123")
//                .status(SessionStatus.AUTHENTICATED)
//                .createdAt(LocalDateTime.now())
//                .lastAccessedAt(LocalDateTime.now().minusMinutes(5))
//                .lastHeartbeat(LocalDateTime.now().minusMinutes(5))
//                .expiresAt(LocalDateTime.now().plusMinutes(30))
//                .build();
//
//        when(valueOperations.get("ds:session:" + sessionId)).thenReturn(mockSession);
//
//        // When
//        sessionService.updateHeartbeat(sessionId);
//
//        // Then
//        verify(valueOperations, times(2)).set(eq("ds:session:" + sessionId), any(UserSession.class), eq(1800), eq(TimeUnit.SECONDS));
//    }
//
//    @Test
//    void disconnectBySocketId_ShouldDisconnectSession() {
//        // Given
//        String socketId = "socket123";
//        String sessionId = "session123";
//        UserSession mockSession = UserSession.builder()
//                .sessionId(sessionId)
//                .socketId(socketId)
//                .status(SessionStatus.AUTHENTICATED)
//                .createdAt(LocalDateTime.now())
//                .lastAccessedAt(LocalDateTime.now())
//                .expiresAt(LocalDateTime.now().plusMinutes(30))
//                .build();
//
//        when(sessionRedisTemplate.opsForValue()).thenReturn(stringValueOperations);
//        when(stringValueOperations.get("ds:session:socket:" + socketId)).thenReturn(sessionId);
//        when(valueOperations.get("ds:session:" + sessionId)).thenReturn(mockSession);
//
//        // When
//        sessionService.disconnectBySocketId(socketId);
//
//        // Then
//        verify(valueOperations).set(eq("ds:session:" + sessionId), any(UserSession.class), eq(1800), eq(TimeUnit.SECONDS));
//        verify(sessionRedisTemplate).delete("ds:session:socket:" + socketId);
//    }
//}
