// server.js
const express = require('express');
const cors = require('cors');
const http = require('http');
const { Server } = require('socket.io');
// // (Tuỳ chọn) Redis adapter để scale ngang nhiều instance
// const { createAdapter } = require('@socket.io/redis-adapter');
// const { createClient } = require('redis');

const app = express();
const PORT = 3001;

// ───────────────────────────────────────────────────────────────
// Middlewares
// ───────────────────────────────────────────────────────────────
app.use(cors());
app.use(express.json());

// ───────────────────────────────────────────────────────────────
// In-memory storage (demo)
// ───────────────────────────────────────────────────────────────
const users = {
    'testuser': { password: 'testpass', name: 'Test User', email: '<EMAIL>' },
    'admin':    { password: 'admin123', name: 'Administrator', email: '<EMAIL>' },
    'user1':    { password: 'pass123',  name: 'User One',      email: '<EMAIL>' }
};
const sessions = new Map();       // sessionId -> session info
const socketToSession = new Map(); // socketId  -> sessionId (tham chiếu nhanh)

// ───────────────────────────────────────────────────────────────
// HTTP + Socket.IO
// ───────────────────────────────────────────────────────────────
const httpServer = http.createServer(app);

const io = new Server(httpServer, {
    cors: {
        origin: true, // nên chỉ định domain production để an toàn
        credentials: true,
        methods: ['GET', 'POST']
    },
    path: '/socket.io/',
    // allowEIO3: true, // bật nếu bạn có client Socket.IO v2 rất cũ
});

// // (Tuỳ chọn) Redis adapter để scale
// const pubClient = createClient({ url: 'redis://localhost:6379' });
// const subClient = pubClient.duplicate();
// Promise.all([pubClient.connect(), subClient.connect()]).then(() => {
//   io.adapter(createAdapter(pubClient, subClient));
//   console.log('Socket.IO is using Redis adapter');
// });

// ───────────────────────────────────────────────────────────────
// Socket.IO handlers
// ───────────────────────────────────────────────────────────────
io.use((socket, next) => {
    // Có thể kiểm tra token ở đây nếu muốn:
    // const token = socket.handshake.auth?.token || socket.handshake.query?.token;
    // if (!token) return next(new Error('unauthorized'));
    return next();
});

io.on('connection', (socket) => {
    const ua = socket.handshake.headers['user-agent'];
    console.log('[IO] connected', socket.id, '| UA:', ua);

    // Ping/Pong ứng dụng
    socket.on('ping:app', () => socket.emit('pong:app', Date.now()));

    // Join 1 room hoặc nhiều room
    socket.on('join', (rooms) => {
        const list = Array.isArray(rooms) ? rooms : [rooms];
        socket.join(list);
        console.log('[IO] join', socket.id, '->', list);
        socket.emit('joined', list);
    });

    // Khi client logout qua socket (tuỳ chọn)
    socket.on('auth:logout', () => {
        const sid = socketToSession.get(socket.id);
        if (sid && sessions.has(sid)) sessions.delete(sid);
        socketToSession.delete(socket.id);
        socket.emit('auth:loggedOut');
        socket.disconnect(true);
    });

    socket.on('disconnect', (reason) => {
        console.log('[IO] disconnected', socket.id, '| reason:', reason);
        const sid = socketToSession.get(socket.id);
        if (sid) {
            // không xoá session ngay (tuỳ yêu cầu), chỉ bỏ liên kết socketId
            const sess = sessions.get(sid);
            if (sess) {
                sess.socketId = null;
                sess.lastActivity = new Date().toISOString();
            }
            socketToSession.delete(socket.id);
        }
    });
});

// ───────────────────────────────────────────────────────────────
// REST endpoints
// ───────────────────────────────────────────────────────────────

// Health check
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage()
    });
});

// Stats
app.get('/stats', (req, res) => {
    res.json({
        activeSessions: sessions.size,
        totalUsers: Object.keys(users).length,
        connectedSockets: io.of('/').sockets.size,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
    });
});

// Login
app.post('/auth/login', (req, res) => {
    const { messageId, sessionId, username, password, socketId, clientIp, userAgent, metadata } = req.body;

    console.log(`Login request - User: ${username}, Session: ${sessionId}, Socket: ${socketId}`);

    setTimeout(() => {
        const user = users[username];

        if (!user || user.password !== password) {
            console.log(`[AUTH] Login failed for user: ${username}`);
            return res.json({
                messageId,
                sessionId,
                success: false,
                message: 'Invalid username or password'
            });
        }

        // Kiểm tra socketId có đang kết nối không (nếu client gửi lên)
        const hasSocket = socketId && io.of('/').sockets.get(socketId);
        if (!hasSocket) {
            console.warn(`[AUTH] socketId not found or disconnected: ${socketId}`);
            // vẫn cho login, nhưng không thể emit realtime ngay lúc này
            // (client nên login sau khi connect socket và gửi socket.id)
        }

        // Generate tokens
        const accessToken  = `access_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
        const refreshToken = `refresh_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
        const connectionId = `conn_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
        const userId = `user_${username}_${Date.now()}`;

        // Lưu session
        const sess = {
            userId,
            username,
            accessToken,
            refreshToken,
            connectionId,
            socketId: hasSocket ? socketId : null,
            clientIp,
            userAgent,
            loginTime: new Date().toISOString(),
            lastActivity: new Date().toISOString()
        };
        sessions.set(sessionId, sess);

        // Liên kết 2 chiều socketId ↔ sessionId
        if (hasSocket) {
            socketToSession.set(socketId, sessionId);

            // Join room dành riêng cho user
            io.to(socketId).socketsJoin([`u:${userId}`]);

            // Emit thông báo login thành công qua socket
            io.to(socketId).emit('auth:ok', {
                sessionId,
                userId,
                accessToken,
                refreshToken,
                connectionId,
                userInfo: {
                    name: user.name,
                    email: user.email,
                    username,
                    loginTime: sess.loginTime
                }
            });
        }

        console.log(`[AUTH] Login OK for ${username} -> userId=${userId}, socket=${socketId}`);

        res.json({
            messageId,
            sessionId,
            success: true,
            message: 'Login successful',
            data: {
                userId,
                accessToken,
                refreshToken,
                connectionId,
                userInfo: {
                    name: user.name,
                    email: user.email,
                    username,
                    loginTime: sess.loginTime
                }
            }
        });
    }, 100);
});

// Data
app.post('/api/data', (req, res) => {
    const { messageId, sessionId, userId, accessToken, action, payload, metadata } = req.body;

    console.log(`Data request - Session: ${sessionId}, Action: ${action}`);

    const session = sessions.get(sessionId);
    if (!session || session.accessToken !== accessToken) {
        console.log(`[DATA] Invalid session or token. sessionId=${sessionId}`);
        return res.json({
            messageId,
            sessionId,
            success: false,
            message: 'Invalid session or access token'
        });
    }

    session.lastActivity = new Date().toISOString();

    let responseData = {};

    switch (action) {
        case 'getUserData':
            responseData = {
                user: {
                    userId: session.userId,
                    username: session.username,
                    name: users[session.username].name,
                    email: users[session.username].email,
                    loginTime: session.loginTime,
                    lastActivity: session.lastActivity
                }
            };
            break;

        case 'getSystemInfo':
            responseData = {
                system: {
                    serverTime: new Date().toISOString(),
                    uptime: process.uptime(),
                    activeSessions: sessions.size,
                    nodeVersion: process.version,
                    connectedSockets: io.of('/').sockets.size
                }
            };
            break;

        case 'processData':
            responseData = {
                result: 'processed',
                input: payload,
                processedAt: new Date().toISOString(),
                processingTime: Math.random() * 100
            };
            break;

        default:
            responseData = {
                message: `Action '${action}' processed successfully`,
                payload,
                timestamp: new Date().toISOString()
            };
    }

    // (Tuỳ chọn) push realtime kết quả về socket (nếu có)
    if (session.socketId && io.of('/').sockets.get(session.socketId)) {
        io.to(session.socketId).emit('api:response', {
            action,
            data: responseData,
            messageId,
            sessionId,
            pushedAt: new Date().toISOString()
        });
    }

    console.log(`[DATA] processed. session=${sessionId}, action=${action}`);
    res.json({
        messageId,
        sessionId,
        success: true,
        message: 'Request processed successfully',
        data: responseData,
        metadata: {
            processedAt: new Date().toISOString(),
            processingNode: 'nodejs-server'
        }
    });
});

// Logout
app.post('/auth/logout', (req, res) => {
    const { sessionId, accessToken } = req.body;

    const session = sessions.get(sessionId);
    if (session && session.accessToken === accessToken) {
        // bắn sự kiện qua socket (nếu còn online)
        if (session.socketId && io.of('/').sockets.get(session.socketId)) {
            io.to(session.socketId).emit('auth:loggedOut');
            io.sockets.sockets.get(session.socketId)?.disconnect(true);
        }
        sessions.delete(sessionId);
        socketToSession.forEach((sid, sockId) => {
            if (sid === sessionId) socketToSession.delete(sockId);
        });

        console.log(`[AUTH] Session logged out: ${sessionId}`);
        res.json({ success: true, message: 'Logged out successfully' });
    } else {
        res.json({ success: false, message: 'Invalid session' });
    }
});

// Push qua room/user (ví dụ: gửi thông báo đến tất cả thiết bị của user)
app.post('/push/to-user', (req, res) => {
    const { userId, event = 'push', payload = {} } = req.body;
    io.to(`u:${userId}`).emit(event, payload);
    res.json({ ok: true, pushed: { room: `u:${userId}`, event } });
});

// Push trực tiếp đến 1 socketId
app.post('/push/to-socket', (req, res) => {
    const { socketId, event = 'push', payload = {} } = req.body;
    if (io.of('/').sockets.get(socketId)) {
        io.to(socketId).emit(event, payload);
        return res.json({ ok: true, pushed: { socketId, event } });
    }
    res.status(404).json({ ok: false, error: 'Socket not found' });
});

// Session cleanup (5 phút/lần)
setInterval(() => {
    const now = Date.now();
    let cleaned = 0;

    for (const [sessionId, session] of sessions.entries()) {
        const last = new Date(session.lastActivity).getTime();
        const diff = now - last;
        if (diff > 30 * 60 * 1000) { // > 30 phút
            sessions.delete(sessionId);
            cleaned++;
        }
    }

    if (cleaned > 0) {
        console.log(`Cleaned up ${cleaned} expired sessions`);
    }
}, 5 * 60 * 1000);

// Error handling
app.use((err, req, res, next) => {
    console.error('Server error:', err);
    res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: err.message
    });
});

// Start server (HTTP + Socket.IO)
httpServer.listen(PORT, () => {
    console.log(`Node.js Auth + Socket.IO Server running on port ${PORT}`);
    console.log(`Health: http://localhost:${PORT}/health`);
    console.log(`Stats : http://localhost:${PORT}/stats`);
    console.log('');
    console.log('Available test users:');
    Object.keys(users).forEach(username => {
        console.log(`  - ${username} / ${users[username].password}`);
    });
});
