{"name": "ds-socket-login-nodejs-server", "version": "1.0.0", "description": "Node.js Authentication Server for DS Socket Login System", "main": "nodejs-server-example.js", "scripts": {"start": "node nodejs-server-example.js", "dev": "nodemon nodejs-server-example.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "authentication", "socket.io", "express"], "author": "DS Socket Login System", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}