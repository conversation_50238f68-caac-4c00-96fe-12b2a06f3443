<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DS Socket Login - Test Client</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.authenticated { background-color: #d1ecf1; color: #0c5460; }
        
        input, button, textarea {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover { background-color: #0056b3; }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .log-container {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #0066cc; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        
        .form-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .stat-item {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <h1>DS Socket Login System - Test Client</h1>
    
    <!-- Connection Status -->
    <div class="container">
        <h2>Connection Status</h2>
        <div id="connectionStatus" class="status disconnected">Disconnected</div>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="sessionId">-</div>
                <div>Session ID</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="socketId">-</div>
                <div>Socket ID</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="userId">-</div>
                <div>User ID</div>
            </div>
        </div>
        <button id="connectBtn" onclick="connect()">Connect</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
    </div>
    

    <div class="container" style="flex-direction: row; justify-content: space-between; display: flex ">
        <!-- Authentication -->
        <div class="container" style="flex: 1">
            <h2>Authentication</h2>
            <div class="form-group">
                <label>Username:</label>
                <input type="text" id="username" value="testuser" />
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input type="password" id="password" value="testpass" />
            </div>
            <button id="loginBtn" onclick="login()" disabled>Login</button>
            <div id="authStatus" class="status" style="display: none;"></div>
        </div>
        <!-- Data Request -->
        <div class="container" style="flex: 1">
            <h2>Data Request</h2>
            <div class="form-group">
                <label>Action:</label>
                <input type="text" id="action" value="getUserData" />
            </div>
            <div class="form-group">
                <label>Payload:</label>
                <textarea id="payload" rows="3" cols="50">{"param1": "value1", "param2": "value2"}</textarea>
            </div>
            <button id="dataBtn" onclick="sendDataRequest()" >Send Data Request</button>
        </div>

    </div>

    <!-- Heartbeat -->
<!--    <div class="container">-->
<!--        <h2>Heartbeat</h2>-->
<!--        <button id="heartbeatBtn" onclick="sendHeartbeat()" disabled>Send Heartbeat</button>-->
<!--        <label>-->
<!--            <input type="checkbox" id="autoHeartbeat" onchange="toggleAutoHeartbeat()"> Auto Heartbeat (30s)-->
<!--        </label>-->
<!--        <div>Last Heartbeat: <span id="lastHeartbeat">Never</span></div>-->
<!--    </div>-->
    
    <!-- Event Log -->
    <div class="container">
        <h2>Event Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="eventLog" class="log-container"></div>
    </div>

    <script>
        let socket = null;
        let sessionId = null;
        let accessToken = null;
        let heartbeatInterval = null;
        let messageIdCounter = 1;

        function generateMessageId() {
            return 'msg_' + Date.now() + '_' + (messageIdCounter++);
        }

        function log(message, type = 'info') {
            const logContainer = document.getElementById('eventLog');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('eventLog').innerHTML = '';
        }

        function updateConnectionStatus(status, className) {
            const statusEl = document.getElementById('connectionStatus');
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
        }

        function updateStats(data) {
            if (data.sessionId) {
                document.getElementById('sessionId').textContent = data.sessionId.substring(0, 8) + '...';
                sessionId = data.sessionId;
            }
            if (data.socketId) {
                document.getElementById('socketId').textContent = data.socketId.substring(0, 8) + '...';
            }
            if (data.userId) {
                document.getElementById('userId').textContent = data.userId;
            }
        }

        function updateButtonStates(connected, authenticated) {
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('loginBtn').disabled = !connected || authenticated;
            // document.getElementById('dataBtn').disabled = !authenticated;
            document.getElementById('heartbeatBtn').disabled = !connected;
        }

        function connect() {
            if (socket) {
                socket.disconnect();
            }

            log('Connecting to Socket.IO server...', 'info');
            socket = io('http://localhost:9094', {
                transports: ['websocket', 'polling'],
                timeout: 5000,
                query: { MdmTp: '02', DeviceId: "DeviceId-00000001", DeviceName: "DeviceName-00000001" }
            });

            socket.on('connect', () => {
                log('Connected to server', 'success');
                updateConnectionStatus('Connected', 'connected');
                updateButtonStates(true, false);
                updateStats({ socketId: socket.id });
            });

            socket.on('RES_MSG', (data) => {
                log(`Server acknowledgment: ${JSON.stringify(data)}`, 'success');
                updateStats(data);
            });

            socket.on('disconnect', () => {
                log('Disconnected from server', 'warning');
                updateConnectionStatus('Disconnected', 'disconnected');
                updateButtonStates(false, false);
                sessionId = null;
                accessToken = null;
                updateStats({ sessionId: '-', userId: '-' });
            });

            socket.on('login_response', (data) => {
                log(`Login response: ${JSON.stringify(data)}`, data.success ? 'success' : 'error');
                if (data.success) {
                    accessToken = data.data.accessToken;
                    updateStats({ userId: data.data.userId });
                    updateConnectionStatus('Authenticated', 'authenticated');
                    updateButtonStates(true, true);
                    document.getElementById('authStatus').style.display = 'block';
                    document.getElementById('authStatus').className = 'status authenticated';
                    document.getElementById('authStatus').textContent = 'Authenticated successfully';
                } else {
                    document.getElementById('authStatus').style.display = 'block';
                    document.getElementById('authStatus').className = 'status disconnected';
                    document.getElementById('authStatus').textContent = `Login failed: ${data.message}`;
                }
            });

            socket.on('data_response', (data) => {
                log(`Data response: ${JSON.stringify(data)}`, data.success ? 'success' : 'error');
            });

            socket.on('heartbeat_ack', (data) => {
                log(`Heartbeat ACK: ${JSON.stringify(data)}`, 'info');
                document.getElementById('lastHeartbeat').textContent = new Date().toLocaleTimeString();
            });

            socket.on('error', (data) => {
                log(`Error: ${JSON.stringify(data)}`, 'error');
            });

            socket.on('connect_error', (error) => {
                log(`Connection error: ${error.message}`, 'error');
                updateConnectionStatus('Connection Failed', 'disconnected');
            });
        }

        function disconnect() {
            if (socket) {
                socket.disconnect();
            }
            if (heartbeatInterval) {
                clearInterval(heartbeatInterval);
                heartbeatInterval = null;
                document.getElementById('autoHeartbeat').checked = false;
            }
        }

        function login() {
            if (!socket || !sessionId) {
                log('Not connected or no session ID', 'error');
                return;
            }

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const messageId = generateMessageId();

            const loginData = {
                messageId: messageId,
                sessionId: sessionId,
                username: username,
                password: password,
                metadata: {
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent
                }
            };
            const data = {
                messageId: messageId,
                sessionId: sessionId,
                metadata: {
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent
                },
                "CltVersion": "3.1.0",
                "ClientSeq": 22,
                "SecCode": "081",
                "WorkerName": "FOSxID02",
                "ServiceName": "FOSxID02_Login",
                "TimeOut": 15,
                "MWLoginID": "WEB",
                "MWLoginPswd": ",+A,3-)-C.*,6,9,=+F*K.N*M.=+)+J,004",
                "AppLoginID": "",
                "ClientSentTime": "0",
                "Lang": "VI",
                "MdmTp": "02",
                "InVal": [
                    "login",
                    "081c046836",
                    "9-,,G,H,9>0+E-,,9+@",
                    "",
                    "",
                    "N",
                    "browser:Chrome|139,os:Windows|10"
                ],
                "TotInVal": 7,
                "AprStat": "N",
                "Operation": "U",
                "CustMgnBrch": "",
                "CustMgnAgc": "",
                "BrkMgnBrch": "",
                "BrkMgnAgc": "",
                "LoginBrch": "",
                "LoginAgnc": "",
                "AprSeq": "",
                "MakerDt": "",
                "AprID": "",
                "AprAmt": "",
                "Otp": "",
                "AcntNo": "",
                "SubNo": "",
                "BankCd": "",
                "PCName": "",
                "SessionID": ""
            }

            log(`Sending login request: ${JSON.stringify(data)}`, 'info');
            socket.emit('REQ_MSG', data);
        }

        function sendDataRequest() {
            if (!socket ) {
                log('Not authenticated', 'error');
                return;
            }

            const action = document.getElementById('action').value;
            const payloadText = document.getElementById('payload').value;
            const messageId = generateMessageId();

            let payload;
            try {
                payload = JSON.parse(payloadText);
                log(`JSON payload: ${payloadText}`, 'info');

            } catch (e) {
                log(`Invalid JSON payload: ${e.message}`, 'error');
                return;
            }

            const dataRequest = {
                messageId: messageId,
                sessionId: sessionId,
                metadata: {
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent
                },
                ...payload
            }


            log(`Sending data request: ${JSON.stringify(dataRequest)}`, 'info');
            // socket.emit('data_request', dataRequest);
            socket.emit('REQ_MSG', dataRequest);

        }

        function sendHeartbeat() {
            if (!socket || !sessionId) {
                log('Not connected', 'error');
                return;
            }

            const heartbeatData = {
                sessionId: sessionId,
                accessToken: accessToken,
                timestamp: Date.now()
            };

            log(`Sending heartbeat: ${JSON.stringify(heartbeatData)}`, 'info');
            socket.emit('heartbeat', heartbeatData);
        }

        function toggleAutoHeartbeat() {
            const checkbox = document.getElementById('autoHeartbeat');
            if (checkbox.checked) {
                heartbeatInterval = setInterval(sendHeartbeat, 30000); // 30 seconds
                log('Auto heartbeat enabled (30s interval)', 'info');
            } else {
                if (heartbeatInterval) {
                    clearInterval(heartbeatInterval);
                    heartbeatInterval = null;
                }
                log('Auto heartbeat disabled', 'info');
            }
        }

        // Initialize
        log('Test client loaded. Click Connect to start.', 'info');
    </script>
</body>
</html>
