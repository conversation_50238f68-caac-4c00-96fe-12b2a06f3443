2025-09-03 08:13:01 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 08:13:01 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-09-03 08:13:03 [SpringApplicationShutdownHook] INFO  c.e.d.s.NodeJsSocketConnectionManager - Shutting down Node.js Socket Connection Manager
2025-09-03 08:13:03 [SpringApplicationShutdownHook] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager shutdown complete
2025-09-03 08:13:03 [SpringApplicationShutdownHook] INFO  c.e.d.config.SocketIOConfig - Stopping SocketIO Server...
2025-09-03 08:29:59 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 16496 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 08:29:59 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 08:30:00 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 08:30:00 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 08:30:00 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-09-03 08:30:00 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 08:30:00 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 08:30:00 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 08:30:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 08:30:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 08:30:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 08:30:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 08:30:00 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 08:30:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 08:30:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 08:30:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 08:30:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 08:30:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 08:30:00 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 08:30:01 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-09-03 08:30:01 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-09-03 08:30:01 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-09-03 08:30:01 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-09-03 08:30:01 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.631 seconds (process running for 4.605)
2025-09-03 08:30:29 [parallel-1] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 08:31:12 [reactor-http-nio-3] WARN  c.e.d.s.NodeJsSocketConnectionManager - No active Node.js connection for data: {CltVersion=3.1.0, ClientSeq=14, SecCode=081, WorkerName=FOSqNews, ServiceName=FOSqNews_Suggest, TimeOut=15, MWLoginID=WEB, MWLoginPswd=,+A,3-)-C.*,6,9,=+F*K.N*M.=+)+J,004, AppLoginID=, ClientSentTime=0, Lang=VI, MdmTp=02, InVal=[01], TotInVal=1, AprStat=N, Operation=Q, CustMgnBrch=, CustMgnAgc=, BrkMgnBrch=, BrkMgnAgc=, LoginBrch=, LoginAgnc=, AprSeq=, MakerDt=, AprID=, AprAmt=, Otp=, AcntNo=, SubNo=, BankCd=, PCName=, SessionID=}
2025-09-03 08:35:06 [parallel-2] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 08:35:39 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 08:35:39 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-09-03 08:35:43 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 14756 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 08:35:43 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 08:35:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 08:35:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 08:35:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-09-03 08:35:44 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 08:35:44 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 08:35:44 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 08:35:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 08:35:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 08:35:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 08:35:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 08:35:45 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 08:35:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 08:35:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 08:35:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 08:35:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 08:35:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 08:35:45 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 08:35:45 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-09-03 08:35:45 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-09-03 08:35:45 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-09-03 08:35:46 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-09-03 08:35:46 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.476 seconds (process running for 3.237)
2025-09-03 08:35:51 [parallel-1] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 08:36:50 [reactor-http-nio-3] WARN  c.e.d.s.NodeJsSocketConnectionManager - No active Node.js connection for data: {CltVersion=3.1.0, ClientSeq=14, SecCode=081, WorkerName=FOSqNews, ServiceName=FOSqNews_Suggest, TimeOut=15, MWLoginID=WEB, MWLoginPswd=,+A,3-)-C.*,6,9,=+F*K.N*M.=+)+J,004, AppLoginID=, ClientSentTime=0, Lang=VI, MdmTp=02, InVal=[01], TotInVal=1, AprStat=N, Operation=Q, CustMgnBrch=, CustMgnAgc=, BrkMgnBrch=, BrkMgnAgc=, LoginBrch=, LoginAgnc=, AprSeq=, MakerDt=, AprID=, AprAmt=, Otp=, AcntNo=, SubNo=, BankCd=, PCName=, SessionID=}
2025-09-03 08:36:50 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 08:36:50 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-09-03 08:36:53 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 17968 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 08:36:53 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 08:36:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 08:36:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 08:36:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-09-03 08:36:54 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 08:36:54 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 08:36:54 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 08:36:54 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 08:36:54 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 08:36:54 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 08:36:54 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 08:36:54 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 08:36:54 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 08:36:54 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 08:36:54 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 08:36:54 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 08:36:54 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 08:36:54 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 08:36:54 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-09-03 08:36:54 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-09-03 08:36:54 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-09-03 08:36:55 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-09-03 08:36:55 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.455 seconds (process running for 3.329)
2025-09-03 13:53:42 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 13:53:42 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-09-03 13:53:45 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 76296 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 13:53:45 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 13:53:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 13:53:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 13:53:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-09-03 13:53:47 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 13:53:47 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 13:53:47 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 13:53:47 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 13:53:47 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 13:53:47 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 13:53:47 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 13:53:47 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 13:53:47 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 13:53:47 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 13:53:47 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 13:53:47 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 13:53:47 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 13:53:47 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 13:53:47 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-09-03 13:53:47 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-09-03 13:53:47 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-09-03 13:53:48 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-09-03 13:53:48 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.764 seconds (process running for 3.628)
2025-09-03 13:55:09 [parallel-1] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 13:55:13 [reactor-http-nio-3] WARN  c.e.d.s.NodeJsSocketConnectionManager - No active Node.js connection for data: {CltVersion=3.1.0, ClientSeq=14, SecCode=081, WorkerName=FOSqNews, ServiceName=FOSqNews_Suggest, TimeOut=15, MWLoginID=WEB, MWLoginPswd=,+A,3-)-C.*,6,9,=+F*K.N*M.=+)+J,004, AppLoginID=, ClientSentTime=0, Lang=VI, MdmTp=02, InVal=[01], TotInVal=1, AprStat=N, Operation=Q, CustMgnBrch=, CustMgnAgc=, BrkMgnBrch=, BrkMgnAgc=, LoginBrch=, LoginAgnc=, AprSeq=, MakerDt=, AprID=, AprAmt=, Otp=, AcntNo=, SubNo=, BankCd=, PCName=, SessionID=}
2025-09-03 13:59:36 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 13:59:36 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-09-03 13:59:40 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 60336 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 13:59:40 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 13:59:41 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 13:59:41 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 13:59:41 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-09-03 13:59:42 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 13:59:42 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 13:59:42 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 13:59:42 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 13:59:42 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 13:59:42 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 13:59:42 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 13:59:42 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 13:59:42 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 13:59:42 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 13:59:42 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 13:59:42 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 13:59:42 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 13:59:42 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 13:59:42 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-09-03 13:59:42 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-09-03 13:59:43 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-09-03 13:59:43 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-09-03 13:59:43 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.842 seconds (process running for 3.647)
2025-09-03 14:00:19 [parallel-1] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:00:52 [parallel-3] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:00:56 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 14:00:58 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 72904 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 14:00:58 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 14:00:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 14:00:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 14:00:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-09-03 14:00:59 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 14:00:59 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 14:00:59 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 14:00:59 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 14:01:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 14:01:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 14:01:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 14:01:00 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 14:01:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 14:01:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 14:01:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 14:01:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 14:01:00 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 14:01:00 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 14:01:00 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-09-03 14:01:00 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-09-03 14:01:00 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-09-03 14:01:01 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-09-03 14:01:01 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.837 seconds (process running for 4.097)
2025-09-03 14:01:14 [parallel-1] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:02:44 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 14:02:49 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 55068 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 14:02:49 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 14:02:50 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 14:02:50 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 14:02:50 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-09-03 14:02:50 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 75020 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 14:02:50 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 14:02:50 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 14:02:50 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 14:02:50 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 14:02:50 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 14:02:51 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 14:02:51 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 14:02:51 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 14:02:51 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 14:02:51 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 14:02:51 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 14:02:51 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 14:02:51 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 14:02:51 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 14:02:51 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 14:02:51 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-09-03 14:02:51 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-09-03 14:02:51 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 14:02:51 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 14:02:51 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-09-03 14:02:51 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-09-03 14:02:51 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 14:02:51 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 14:02:51 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 14:02:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 14:02:52 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-09-03 14:02:52 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 3.369 seconds (process running for 4.091)
2025-09-03 14:02:52 [parallel-1] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:02:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 14:02:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 14:02:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 14:02:52 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 14:02:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 14:02:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 14:02:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 14:02:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 14:02:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 14:02:52 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 14:02:52 [nioEventLoopGroup-5-1] ERROR c.c.socketio.SocketIOServer - SocketIO server start failed at port: 9094!
2025-09-03 14:02:52 [main] ERROR c.e.d.config.SocketIOConfig - Failed to start SocketIO Server
java.net.BindException: Address already in use: bind
	at java.base/sun.nio.ch.Net.bind0(Native Method)
	at java.base/sun.nio.ch.Net.bind(Net.java:565)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:344)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:301)
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:561)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1281)
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600)
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579)
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:922)
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:259)
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-09-03 14:02:52 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'socketIOServerRunner' defined in class path resource [com/example/dssocketlogin/config/SocketIOConfig.class]: Failed to instantiate [com.example.dssocketlogin.config.SocketIOConfig$SocketIOServerRunner]: Factory method 'socketIOServerRunner' threw exception with message: Failed to start SocketIO Server
2025-09-03 14:02:52 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Shutting down Node.js Socket Connection Manager
2025-09-03 14:02:52 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager shutdown complete
2025-09-03 14:02:52 [main] INFO  c.e.d.config.SocketIOConfig - Stopping SocketIO Server...
2025-09-03 14:02:56 [main] INFO  c.c.socketio.SocketIOServer - SocketIO server stopped
2025-09-03 14:02:56 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-03 14:02:56 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'socketIOServerRunner' defined in class path resource [com/example/dssocketlogin/config/SocketIOConfig.class]: Failed to instantiate [com.example.dssocketlogin.config.SocketIOConfig$SocketIOServerRunner]: Factory method 'socketIOServerRunner' threw exception with message: Failed to start SocketIO Server
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1351)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:288)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1122)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1093)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1030)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.refresh(ReactiveWebServerApplicationContext.java:66)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.example.dssocketlogin.DsSocketLoginApplication.main(DsSocketLoginApplication.java:12)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.example.dssocketlogin.config.SocketIOConfig$SocketIOServerRunner]: Factory method 'socketIOServerRunner' threw exception with message: Failed to start SocketIO Server
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 21 common frames omitted
Caused by: java.lang.RuntimeException: Failed to start SocketIO Server
	at com.example.dssocketlogin.config.SocketIOConfig$SocketIOServerRunner.startServer(SocketIOConfig.java:143)
	at com.example.dssocketlogin.config.SocketIOConfig$SocketIOServerRunner.<init>(SocketIOConfig.java:134)
	at com.example.dssocketlogin.config.SocketIOConfig.socketIOServerRunner(SocketIOConfig.java:115)
	at com.example.dssocketlogin.config.SocketIOConfig$$SpringCGLIB$$0.CGLIB$socketIOServerRunner$2(<generated>)
	at com.example.dssocketlogin.config.SocketIOConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:348)
	at com.example.dssocketlogin.config.SocketIOConfig$$SpringCGLIB$$0.socketIOServerRunner(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 24 common frames omitted
Caused by: java.net.BindException: Address already in use: bind
	at java.base/sun.nio.ch.Net.bind0(Native Method)
	at java.base/sun.nio.ch.Net.bind(Net.java:565)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:344)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:301)
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:561)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1281)
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600)
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579)
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:922)
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:259)
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-09-03 14:03:10 [RMI TCP Connection(1)-10.22.22.76] WARN  o.s.b.a.health.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration$AdaptedReactiveHealthContributors$1 (redis) took 17818ms to respond
2025-09-03 14:03:18 [parallel-3] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:04:42 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 14:04:42 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-09-03 14:04:47 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 72664 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 14:04:47 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 14:04:48 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 14:04:48 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 14:04:48 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-09-03 14:04:48 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 14:04:48 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 14:04:48 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 14:04:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 14:04:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 14:04:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 14:04:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 14:04:49 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 14:04:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 14:04:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 14:04:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 14:04:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 14:04:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 14:04:49 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 14:04:49 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-09-03 14:04:49 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-09-03 14:04:49 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-09-03 14:04:50 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-09-03 14:04:50 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.766 seconds (process running for 4.078)
2025-09-03 14:04:53 [parallel-1] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:05:26 [parallel-3] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:05:34 [parallel-5] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:05:35 [parallel-7] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:05:36 [parallel-9] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:05:36 [parallel-11] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:05:37 [parallel-1] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:05:38 [parallel-3] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:05:38 [parallel-5] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:05:39 [parallel-7] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:05:40 [parallel-9] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:05:40 [parallel-11] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:05:41 [parallel-1] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:06:02 [parallel-3] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:06:03 [parallel-5] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:06:03 [parallel-7] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:09:37 [parallel-9] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:09:56 [parallel-11] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:09:57 [parallel-1] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:09:58 [parallel-3] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:12:32 [parallel-5] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:31:27 [parallel-7] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:43:39 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 14:43:39 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-09-03 14:43:44 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 72512 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 14:43:44 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 14:43:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 14:43:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 14:43:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-09-03 14:43:45 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 14:43:45 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 14:43:45 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 14:43:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 14:43:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 14:43:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 14:43:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 14:43:45 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 14:43:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 14:43:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 14:43:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 14:43:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 14:43:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 14:43:45 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 14:43:45 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-09-03 14:43:45 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-09-03 14:43:46 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-09-03 14:43:46 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-09-03 14:43:46 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.637 seconds (process running for 3.417)
2025-09-03 14:53:40 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 14:53:40 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-09-03 14:53:43 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 77200 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 14:53:43 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 14:53:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 14:53:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 14:53:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-09-03 14:53:45 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 14:53:45 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 14:53:45 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 14:53:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 14:53:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 14:53:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 14:53:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 14:53:45 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 14:53:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 14:53:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 14:53:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 14:53:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 14:53:45 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 14:53:45 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 14:53:45 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-09-03 14:53:45 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-09-03 14:53:45 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-09-03 14:53:46 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-09-03 14:53:46 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.722 seconds (process running for 3.535)
2025-09-03 14:53:48 [parallel-1] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:54:02 [parallel-3] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:56:15 [parallel-5] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:56:27 [parallel-7] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 14:56:45 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 14:56:45 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-09-03 14:56:50 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 80832 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 14:56:50 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 14:56:51 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 14:56:51 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 14:56:51 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-09-03 14:56:52 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 14:56:52 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 14:56:52 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 14:56:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 14:56:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 14:56:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 14:56:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 14:56:52 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 14:56:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 14:56:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 14:56:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 14:56:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 14:56:52 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 14:56:52 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 14:56:52 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-09-03 14:56:52 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-09-03 14:56:52 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-09-03 14:56:53 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-09-03 14:56:53 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.544 seconds (process running for 3.497)
2025-09-03 15:15:57 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 15:15:57 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-09-03 15:16:01 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 85440 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-09-03 15:16:01 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-03 15:16:02 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 15:16:02 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 15:16:02 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-09-03 15:16:02 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-09-03 15:16:02 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-09-03 15:16:02 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-09-03 15:16:02 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-09-03 15:16:03 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-09-03 15:16:03 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-09-03 15:16:03 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-09-03 15:16:03 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-09-03 15:16:03 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-09-03 15:16:03 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-09-03 15:16:03 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-09-03 15:16:03 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-09-03 15:16:03 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-09-03 15:16:03 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-09-03 15:16:03 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-09-03 15:16:03 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-09-03 15:16:03 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-09-03 15:16:03 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-09-03 15:16:03 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.809 seconds (process running for 3.671)
2025-09-03 15:16:07 [parallel-1] INFO  c.e.d.service.wso2.AuthWebFilter - Auth header: null
2025-09-03 17:37:41 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-03 17:37:41 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-09-03 17:37:43 [SpringApplicationShutdownHook] INFO  c.e.d.s.NodeJsSocketConnectionManager - Shutting down Node.js Socket Connection Manager
2025-09-03 17:37:43 [SpringApplicationShutdownHook] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager shutdown complete
2025-09-03 17:37:43 [SpringApplicationShutdownHook] INFO  c.e.d.config.SocketIOConfig - Stopping SocketIO Server...
2025-09-03 17:37:47 [SpringApplicationShutdownHook] INFO  c.c.socketio.SocketIOServer - SocketIO server stopped
