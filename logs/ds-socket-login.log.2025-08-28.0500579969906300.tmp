2025-08-28 10:40:37 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-28 10:40:37 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-08-28 10:40:45 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 68692 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-08-28 10:40:45 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-28 10:40:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-28 10:40:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-28 10:40:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-28 10:40:46 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'wso2Controller' defined in file [D:\Source\DSSocketLogin\target\classes\com\example\dssocketlogin\controller\Wso2Controller.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'wso2Service' defined in file [D:\Source\DSSocketLogin\target\classes\com\example\dssocketlogin\service\wso2\Wso2Service.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.springframework.web.reactive.function.client.WebClient' available: expected single matching bean but found 2: webClient,nodeJsWebClient
2025-08-28 10:40:46 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-28 10:40:47 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.dssocketlogin.service.wso2.Wso2Service required a single bean, but 2 were found:
	- webClient: defined by method 'webClient' in class path resource [com/example/dssocketlogin/config/WebFluxConfig.class]
	- nodeJsWebClient: defined by method 'nodeJsWebClient' in class path resource [com/example/dssocketlogin/config/WebFluxConfig.class]

This may be due to missing parameter name information

Action:

Consider marking one of the beans as @Primary, updating the consumer to accept multiple beans, or using @Qualifier to identify the bean that should be consumed

Ensure that your compiler is configured to use the '-parameters' flag.
You may need to update both your build tool settings as well as your IDE.
(See https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention)


2025-08-28 10:40:47 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 93548 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-08-28 10:40:47 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-28 10:40:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-28 10:40:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-28 10:40:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-08-28 10:40:48 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'wso2Controller' defined in file [D:\Source\DSSocketLogin\target\classes\com\example\dssocketlogin\controller\Wso2Controller.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'wso2Service' defined in file [D:\Source\DSSocketLogin\target\classes\com\example\dssocketlogin\service\wso2\Wso2Service.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.springframework.web.reactive.function.client.WebClient' available: expected single matching bean but found 2: webClient,nodeJsWebClient
2025-08-28 10:40:48 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-28 10:40:48 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.dssocketlogin.service.wso2.Wso2Service required a single bean, but 2 were found:
	- webClient: defined by method 'webClient' in class path resource [com/example/dssocketlogin/config/WebFluxConfig.class]
	- nodeJsWebClient: defined by method 'nodeJsWebClient' in class path resource [com/example/dssocketlogin/config/WebFluxConfig.class]

This may be due to missing parameter name information

Action:

Consider marking one of the beans as @Primary, updating the consumer to accept multiple beans, or using @Qualifier to identify the bean that should be consumed

Ensure that your compiler is configured to use the '-parameters' flag.
You may need to update both your build tool settings as well as your IDE.
(See https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention)


2025-08-28 10:42:07 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 85604 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-08-28 10:42:07 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-28 10:42:08 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-28 10:42:08 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-28 10:42:08 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-08-28 10:42:08 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-08-28 10:42:08 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-08-28 10:42:08 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-08-28 10:42:08 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-08-28 10:42:08 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-08-28 10:42:08 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-08-28 10:42:08 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-08-28 10:42:08 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-08-28 10:42:08 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'globalResponseWrapper' defined in file [D:\Source\DSSocketLogin\target\classes\com\example\dssocketlogin\model\global\GlobalResponseWrapper.class]: Unsatisfied dependency expressed through constructor parameter 1: No qualifying bean of type 'org.springframework.web.reactive.accept.RequestedContentTypeResolver' available: expected single matching bean but found 2: requestedContentTypeResolver,webFluxContentTypeResolver
2025-08-28 10:42:08 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Shutting down Node.js Socket Connection Manager
2025-08-28 10:42:08 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager shutdown complete
2025-08-28 10:42:08 [main] INFO  c.e.d.config.SocketIOConfig - Stopping SocketIO Server...
2025-08-28 10:42:08 [main] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'socketIOConfig' threw an exception: java.lang.NullPointerException: Cannot invoke "io.netty.channel.EventLoopGroup.shutdownGracefully()" because "this.bossGroup" is null
2025-08-28 10:42:08 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-28 10:42:08 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 1 of constructor in com.example.dssocketlogin.model.global.GlobalResponseWrapper required a single bean, but 2 were found:
	- requestedContentTypeResolver: defined by method 'requestedContentTypeResolver' in class path resource [com/example/dssocketlogin/config/WebFluxConfig.class]
	- webFluxContentTypeResolver: defined by method 'webFluxContentTypeResolver' in class path resource [org/springframework/boot/autoconfigure/web/reactive/WebFluxAutoConfiguration$EnableWebFluxConfiguration.class]

This may be due to missing parameter name information

Action:

Consider marking one of the beans as @Primary, updating the consumer to accept multiple beans, or using @Qualifier to identify the bean that should be consumed

Ensure that your compiler is configured to use the '-parameters' flag.
You may need to update both your build tool settings as well as your IDE.
(See https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention)


2025-08-28 10:43:08 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 94968 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-08-28 10:43:08 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-28 10:43:09 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-28 10:43:09 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-28 10:43:09 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-08-28 10:43:10 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-08-28 10:43:10 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-08-28 10:43:10 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-08-28 10:43:10 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-08-28 10:43:10 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-08-28 10:43:10 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-08-28 10:43:10 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-08-28 10:43:10 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-08-28 10:43:10 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'globalResponseWrapper' defined in file [D:\Source\DSSocketLogin\target\classes\com\example\dssocketlogin\model\global\GlobalResponseWrapper.class]: Unsatisfied dependency expressed through constructor parameter 1: No qualifying bean of type 'org.springframework.web.reactive.accept.RequestedContentTypeResolver' available: expected single matching bean but found 2: requestedContentTypeResolver,webFluxContentTypeResolver
2025-08-28 10:43:10 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Shutting down Node.js Socket Connection Manager
2025-08-28 10:43:10 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager shutdown complete
2025-08-28 10:43:10 [main] INFO  c.e.d.config.SocketIOConfig - Stopping SocketIO Server...
2025-08-28 10:43:10 [main] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'socketIOConfig' threw an exception: java.lang.NullPointerException: Cannot invoke "io.netty.channel.EventLoopGroup.shutdownGracefully()" because "this.bossGroup" is null
2025-08-28 10:43:10 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-28 10:43:10 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 1 of constructor in com.example.dssocketlogin.model.global.GlobalResponseWrapper required a single bean, but 2 were found:
	- requestedContentTypeResolver: defined by method 'requestedContentTypeResolver' in class path resource [com/example/dssocketlogin/config/WebFluxConfig.class]
	- webFluxContentTypeResolver: defined by method 'webFluxContentTypeResolver' in class path resource [org/springframework/boot/autoconfigure/web/reactive/WebFluxAutoConfiguration$EnableWebFluxConfiguration.class]

This may be due to missing parameter name information

Action:

Consider marking one of the beans as @Primary, updating the consumer to accept multiple beans, or using @Qualifier to identify the bean that should be consumed

Ensure that your compiler is configured to use the '-parameters' flag.
You may need to update both your build tool settings as well as your IDE.
(See https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention)


2025-08-28 10:45:20 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 8812 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-08-28 10:45:20 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-28 10:45:21 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-28 10:45:21 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-28 10:45:21 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-08-28 10:45:21 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-08-28 10:45:21 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-08-28 10:45:22 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-08-28 10:45:22 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-08-28 10:45:22 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-08-28 10:45:22 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-08-28 10:45:22 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-08-28 10:45:22 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-08-28 10:45:22 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'globalResponseWrapper' defined in file [D:\Source\DSSocketLogin\target\classes\com\example\dssocketlogin\model\global\GlobalResponseWrapper.class]: Unsatisfied dependency expressed through constructor parameter 1: No qualifying bean of type 'org.springframework.web.reactive.accept.RequestedContentTypeResolver' available: expected single matching bean but found 2: requestedContentTypeResolver,webFluxContentTypeResolver
2025-08-28 10:45:22 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Shutting down Node.js Socket Connection Manager
2025-08-28 10:45:22 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager shutdown complete
2025-08-28 10:45:22 [main] INFO  c.e.d.config.SocketIOConfig - Stopping SocketIO Server...
2025-08-28 10:45:22 [main] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'socketIOConfig' threw an exception: java.lang.NullPointerException: Cannot invoke "io.netty.channel.EventLoopGroup.shutdownGracefully()" because "this.bossGroup" is null
2025-08-28 10:45:22 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-28 10:45:22 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 1 of constructor in com.example.dssocketlogin.model.global.GlobalResponseWrapper required a single bean, but 2 were found:
	- requestedContentTypeResolver: defined by method 'requestedContentTypeResolver' in class path resource [com/example/dssocketlogin/config/WebFluxConfig.class]
	- webFluxContentTypeResolver: defined by method 'webFluxContentTypeResolver' in class path resource [org/springframework/boot/autoconfigure/web/reactive/WebFluxAutoConfiguration$EnableWebFluxConfiguration.class]

This may be due to missing parameter name information

Action:

Consider marking one of the beans as @Primary, updating the consumer to accept multiple beans, or using @Qualifier to identify the bean that should be consumed

Ensure that your compiler is configured to use the '-parameters' flag.
You may need to update both your build tool settings as well as your IDE.
(See https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention)


2025-08-28 10:45:48 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 92240 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-08-28 10:45:48 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-28 10:45:49 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-28 10:45:49 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-28 10:45:49 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-08-28 10:45:49 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-08-28 10:45:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-08-28 10:45:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-08-28 10:45:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-08-28 10:45:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-08-28 10:45:49 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-08-28 10:45:49 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-08-28 10:45:49 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-08-28 10:45:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-08-28 10:45:49 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-08-28 10:45:50 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-08-28 10:45:50 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-08-28 10:45:50 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-08-28 10:45:50 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-08-28 10:45:50 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-08-28 10:45:50 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-08-28 10:45:50 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-08-28 10:45:50 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-08-28 10:45:50 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.795 seconds (process running for 3.627)
2025-08-28 10:46:48 [nioEventLoopGroup-6-1] WARN  c.c.socketio.handler.WrongUrlHandler - Blocked wrong socket.io-context request! url: /api/wso2/login, params: {}, ip: /127.0.0.1:52523
2025-08-28 10:47:07 [reactor-http-nio-3] WARN  r.n.http.client.HttpClientConnect - [31dbcc7e-1, L:/***********:52538 - R:testid.shinhansec.com.vn/***********:443] The connection observed an error
org.springframework.web.reactive.function.UnsupportedMediaTypeException: Content type 'application/x-www-form-urlencoded' not supported for bodyType=java.util.HashMap<?, ?>
	at org.springframework.web.reactive.function.BodyInserters.unsupportedError(BodyInserters.java:466)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoError] :
	reactor.core.publisher.Mono.error(Mono.java:299)
	org.springframework.web.reactive.function.BodyInserters.writeWithMessageWriters(BodyInserters.java:456)
Error has been observed at the following site(s):
	*__Mono.error ⇢ at org.springframework.web.reactive.function.BodyInserters.writeWithMessageWriters(BodyInserters.java:456)
	*__Mono.defer ⇢ at reactor.netty.http.client.HttpClientConnect$HttpIOHandlerObserver.onStateChange(HttpClientConnect.java:434)
Original Stack Trace:
		at org.springframework.web.reactive.function.BodyInserters.unsupportedError(BodyInserters.java:466)
		at org.springframework.web.reactive.function.BodyInserters.writeWithMessageWriters(BodyInserters.java:456)
		at org.springframework.web.reactive.function.BodyInserters.lambda$fromValue$1(BodyInserters.java:103)
		at org.springframework.web.reactive.function.client.DefaultClientRequestBuilder$BodyInserterRequest.writeTo(DefaultClientRequestBuilder.java:276)
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$exchange$1(ExchangeFunctions.java:103)
		at org.springframework.http.client.reactive.ReactorClientHttpConnector.lambda$connect$2(ReactorClientHttpConnector.java:169)
		at reactor.netty.http.client.HttpClientConnect$HttpClientHandler.requestWithBody(HttpClientConnect.java:594)
		at reactor.netty.http.client.HttpClientConnect$HttpIOHandlerObserver.lambda$onStateChange$0(HttpClientConnect.java:434)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.netty.http.client.HttpClientConnect$HttpIOHandlerObserver.onStateChange(HttpClientConnect.java:435)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onStateChange(DefaultPooledConnectionProvider.java:207)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnection.onStateChange(DefaultPooledConnectionProvider.java:468)
		at reactor.netty.channel.ChannelOperationsHandler.channelActive(ChannelOperationsHandler.java:62)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelActive(AbstractChannelHandlerContext.java:260)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelActive(AbstractChannelHandlerContext.java:238)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelActive(AbstractChannelHandlerContext.java:231)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelActive(CombinedChannelDuplexHandler.java:412)
		at io.netty.channel.ChannelInboundHandlerAdapter.channelActive(ChannelInboundHandlerAdapter.java:69)
		at io.netty.channel.CombinedChannelDuplexHandler.channelActive(CombinedChannelDuplexHandler.java:211)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelActive(AbstractChannelHandlerContext.java:260)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelActive(AbstractChannelHandlerContext.java:238)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelActive(AbstractChannelHandlerContext.java:231)
		at reactor.netty.tcp.SslProvider$SslReadHandler.userEventTriggered(SslProvider.java:723)
		at io.netty.channel.AbstractChannelHandlerContext.invokeUserEventTriggered(AbstractChannelHandlerContext.java:398)
		at io.netty.channel.AbstractChannelHandlerContext.invokeUserEventTriggered(AbstractChannelHandlerContext.java:376)
		at io.netty.channel.AbstractChannelHandlerContext.fireUserEventTriggered(AbstractChannelHandlerContext.java:368)
		at io.netty.handler.ssl.SslHandler.setHandshakeSuccess(SslHandler.java:1979)
		at io.netty.handler.ssl.SslHandler.wrapNonAppData(SslHandler.java:1005)
		at io.netty.handler.ssl.SslHandler.unwrap(SslHandler.java:1539)
		at io.netty.handler.ssl.SslHandler.decodeJdkCompatible(SslHandler.java:1366)
		at io.netty.handler.ssl.SslHandler.decode(SslHandler.java:1415)
		at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:530)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:469)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-28 10:54:25 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 32752 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-08-28 10:54:25 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-28 10:54:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-28 10:54:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-28 10:54:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-08-28 10:54:26 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-08-28 10:54:26 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-08-28 10:54:26 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-08-28 10:54:26 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-08-28 10:54:26 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-08-28 10:54:26 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-08-28 10:54:26 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-08-28 10:54:26 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-08-28 10:54:26 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-08-28 10:54:26 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-08-28 10:54:26 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-08-28 10:54:26 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-08-28 10:54:26 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-08-28 10:54:26 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-08-28 10:54:27 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-08-28 10:54:27 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-08-28 10:54:27 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-08-28 10:54:27 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-08-28 10:54:27 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.709 seconds (process running for 4.067)
2025-08-28 10:56:08 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-28 10:56:08 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-08-28 10:56:10 [SpringApplicationShutdownHook] INFO  c.e.d.s.NodeJsSocketConnectionManager - Shutting down Node.js Socket Connection Manager
2025-08-28 10:56:10 [SpringApplicationShutdownHook] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager shutdown complete
2025-08-28 10:56:10 [SpringApplicationShutdownHook] INFO  c.e.d.config.SocketIOConfig - Stopping SocketIO Server...
2025-08-28 10:56:16 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 78552 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-08-28 10:56:16 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-28 10:56:17 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-28 10:56:17 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-28 10:56:17 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-08-28 10:56:17 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-08-28 10:56:17 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-08-28 10:56:18 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-08-28 10:56:18 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-08-28 10:56:18 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-08-28 10:56:18 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-08-28 10:56:18 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-08-28 10:56:18 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-08-28 10:56:18 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-08-28 10:56:18 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-08-28 10:56:18 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-08-28 10:56:18 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-08-28 10:56:18 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-08-28 10:56:18 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-08-28 10:56:18 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-08-28 10:56:18 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-08-28 10:56:18 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-08-28 10:56:18 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-08-28 10:56:18 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.774 seconds (process running for 3.574)
2025-08-28 10:56:35 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-28 10:56:35 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-08-28 13:36:36 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 51176 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-08-28 13:36:36 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-28 13:36:36 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-28 13:36:36 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-28 13:36:36 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-08-28 13:36:37 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-08-28 13:36:37 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-08-28 13:36:37 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-08-28 13:36:37 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-08-28 13:36:37 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-08-28 13:36:37 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-08-28 13:36:37 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-08-28 13:36:37 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-08-28 13:36:37 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-08-28 13:36:37 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-08-28 13:36:37 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-08-28 13:36:37 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-08-28 13:36:37 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-08-28 13:36:37 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-08-28 13:36:37 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-08-28 13:36:37 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-08-28 13:36:38 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-08-28 13:36:38 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-08-28 13:36:38 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.867 seconds (process running for 3.747)
2025-08-28 13:43:17 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-28 13:43:17 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-08-28 13:43:22 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 18828 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-08-28 13:43:22 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-28 13:43:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-28 13:43:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-28 13:43:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-08-28 13:43:23 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'wso2Controller' defined in file [D:\Source\DSSocketLogin\target\classes\com\example\dssocketlogin\controller\Wso2Controller.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'wso2Service' defined in file [D:\Source\DSSocketLogin\target\classes\com\example\dssocketlogin\service\wso2\Wso2Service.class]: Failed to instantiate [com.example.dssocketlogin.service.wso2.Wso2Service]: Constructor threw exception
2025-08-28 13:43:23 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-28 13:43:23 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'wso2Controller' defined in file [D:\Source\DSSocketLogin\target\classes\com\example\dssocketlogin\controller\Wso2Controller.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'wso2Service' defined in file [D:\Source\DSSocketLogin\target\classes\com\example\dssocketlogin\service\wso2\Wso2Service.class]: Failed to instantiate [com.example.dssocketlogin.service.wso2.Wso2Service]: Constructor threw exception
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1208)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:288)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1122)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1093)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1030)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.refresh(ReactiveWebServerApplicationContext.java:66)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.example.dssocketlogin.DsSocketLoginApplication.main(DsSocketLoginApplication.java:12)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'wso2Service' defined in file [D:\Source\DSSocketLogin\target\classes\com\example\dssocketlogin\service\wso2\Wso2Service.class]: Failed to instantiate [com.example.dssocketlogin.service.wso2.Wso2Service]: Constructor threw exception
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:321)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:309)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1208)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:288)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1568)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1514)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 21 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.example.dssocketlogin.service.wso2.Wso2Service]: Constructor threw exception
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:222)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:145)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:318)
	... 34 common frames omitted
Caused by: java.lang.NullPointerException: Cannot invoke "com.example.dssocketlogin.config.Wso2Config.getSuperUser()" because "this.wso2Config" is null
	at com.example.dssocketlogin.service.wso2.Wso2Service.<init>(Wso2Service.java:29)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:209)
	... 36 common frames omitted
2025-08-28 14:24:37 [main] INFO  c.e.d.DsSocketLoginApplication - Starting DsSocketLoginApplication using Java 21.0.2 with PID 54316 (D:\Source\DSSocketLogin\target\classes started by nhac.vb in D:\Source\DSSocketLogin)
2025-08-28 14:24:37 [main] INFO  c.e.d.DsSocketLoginApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-28 14:24:38 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-28 14:24:38 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-28 14:24:38 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-08-28 14:24:38 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server configured on localhost:9094 with 100 worker threads
2025-08-28 14:24:38 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Redis connection factory configured for *************:6380
2025-08-28 14:24:39 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Anonymous Session RedisTemplate configured
2025-08-28 14:24:39 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Device List RedisTemplate configured
2025-08-28 14:24:39 [main] INFO  c.e.dssocketlogin.config.RedisConfig - User Conn List RedisTemplate configured
2025-08-28 14:24:39 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Initializing Node.js Socket Connection Manager
2025-08-28 14:24:39 [main] INFO  c.e.d.s.NodeJsSocketConnectionManager - Node.js Socket Connection Manager initialized successfully
2025-08-28 14:24:39 [main] INFO  c.e.d.handler.SocketIOEventHandler - [INIT] SocketIO Event Handlers initialized
2025-08-28 14:24:39 [main] INFO  c.e.dssocketlogin.config.RedisConfig - RedisTemplate configured with JSON serialization
2025-08-28 14:24:39 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Session RedisTemplate configured
2025-08-28 14:24:39 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive RedisTemplate configured
2025-08-28 14:24:39 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Session RedisTemplate configured
2025-08-28 14:24:39 [main] INFO  c.e.dssocketlogin.config.RedisConfig - Reactive Anonymous Session RedisTemplate configured
2025-08-28 14:24:39 [main] INFO  c.c.socketio.SocketIOServer - Session store / pubsub factory used: MemoryStoreFactory (local session store only)
2025-08-28 14:24:39 [main] INFO  c.e.d.config.SocketIOConfig - SocketIO Server started successfully on port 9094
2025-08-28 14:24:39 [nioEventLoopGroup-5-1] INFO  c.c.socketio.SocketIOServer - SocketIO server started at port: 9094
2025-08-28 14:24:39 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-28 14:24:39 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 9095 (http)
2025-08-28 14:24:39 [main] INFO  c.e.d.DsSocketLoginApplication - Started DsSocketLoginApplication in 2.553 seconds (process running for 3.388)
