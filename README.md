# DS Socket Login System - Spring Boot 3.4 Implementation

Hệ thống Socket.IO Login hoàn chỉnh với Java Spring Boot 3.4, <PERSON><PERSON> Cache, Netty và WebFlux integration.

## ✨ Tính Năng Đã Implement

### 🏗️ **Kiến Trúc & Công Nghệ**
- ✅ **Spring Boot 3.4** - Framework chính
- ✅ **Netty Socket.IO** - Real-time communication
- ✅ **Spring WebFlux** - Reactive programming
- ✅ **Redis** - Session management & caching
- ✅ **Lettuce** - Redis reactive client
- ✅ **Jackson** - JSON serialization
- ✅ **Micrometer** - Metrics & monitoring

### 🔌 **Socket.IO Server**
- ✅ Event-driven architecture với Netty
- ✅ Connection management & auto-reconnection
- ✅ CORS configuration
- ✅ Custom authentication & authorization
- ✅ Comprehensive error handling

### 📦 **Redis Session Management**
- ✅ Reactive Redis operations
- ✅ Session lifecycle management
- ✅ Automatic expiry & cleanup
- ✅ Connection state tracking
- ✅ Multi-user session limits

### 🌐 **Node.js Integration**
- ✅ WebClient reactive calls
- ✅ Retry mechanism với backoff
- ✅ Health monitoring
- ✅ Circuit breaker pattern
- ✅ Request/response mapping

### 📊 **Monitoring & Health Checks**
- ✅ REST APIs cho monitoring
- ✅ Real-time metrics streaming
- ✅ System statistics
- ✅ Session analytics
- ✅ Performance metrics

### 🧹 **Cleanup & Scheduling**
- ✅ Scheduled session cleanup
- ✅ Stale connection monitoring
- ✅ System maintenance tasks
- ✅ Performance metrics collection
- ✅ Connection pool monitoring

### 🧪 **Testing**
- ✅ Unit tests với Mockito
- ✅ Integration tests
- ✅ Reactive testing với StepVerifier
- ✅ Test client HTML interface
- ✅ Node.js mock server

## Kiến Trúc Tổng Quan

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │   Java Spring   │    │   Node.js       │
│   (Socket.IO)   │<-->│   Boot Server   │<-->│   Server        │
│                 │    │   (Socket.IO)   │    │   (Realtime)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   Redis Cache   │
                       │   (Sessions)    │
                       └─────────────────┘
```

## Tính Năng Chính

### 🔌 Socket.IO Server
- Nhận kết nối từ web client
- Xử lý real-time messaging
- Auto-reconnection support
- CORS configuration

### 🔐 Authentication Flow
- Login request từ client → Java → Node.js
- Access token từ Node.js → Java → Client
- Session management với Redis
- Token validation và refresh

### 📦 Redis Session Management
- Lưu trữ session client-java
- Cache authentication info
- Session expiry management
- Connection state tracking

### 🌐 Node.js Integration
- Persistent connection Java ↔ Node.js
- Message forwarding
- Auto-reconnect mechanism
- Heartbeat monitoring

### 🧹 Cleanup System
- Auto cleanup expired sessions
- Orphaned connection removal
- Memory optimization
- Health monitoring

## 🚀 Cài Đặt và Chạy

### Prerequisites
- ☕ **Java 17+** - JDK 17 hoặc cao hơn
- 📦 **Maven 3.6+** - Build tool
- 🔴 **Redis Server** - Session storage
- 🟢 **Node.js 16+** - Authentication server

### 1. Clone và Build Project
```bash
git clone <repository-url>
cd DSSocketLogin
mvn clean compile
```

**✅ Project đã build thành công với Spring Boot 3.4!**

### 🎯 Quick Start (Recommended)
```bash
# Chạy toàn bộ hệ thống với 1 lệnh
./start-system.sh

# Dừng hệ thống
./stop-system.sh
```

**Scripts sẽ tự động:**
- ✅ Kiểm tra Redis
- ✅ Khởi động Node.js server
- ✅ Build và chạy Spring Boot app
- ✅ Kiểm tra tất cả services
- ✅ Hiển thị URLs và test users

### 2. Cấu Hình Redis
```bash
# Start Redis server
redis-server

# Hoặc với Docker
docker run -d -p 6379:6379 redis:alpine

# Kiểm tra Redis
redis-cli ping
# Kết quả: PONG
```

### 3. Cấu Hình Node.js Authentication Server
Project đã bao gồm Node.js server mẫu hoàn chỉnh:

```bash
# Cài đặt dependencies
npm install

# Chạy Node.js server
npm start
# Server sẽ chạy trên port 3001
```

**Node.js Server Features:**
- ✅ Authentication endpoints
- ✅ Health check & stats
- ✅ Session management
- ✅ Auto cleanup expired sessions
- ✅ Test users: `testuser/testpass`, `admin/admin123`

```javascript
// nodejs-server.js
const io = require('socket.io')(3001);

io.on('connection', (socket) => {
    console.log('Java server connected:', socket.id);
    
    socket.on('login_request', (data) => {
        console.log('Login request:', data);
        
        // Simulate authentication
        const response = {
            messageId: data.messageId,
            sessionId: data.sessionId,
            data: {
                userId: 'user_' + Date.now(),
                accessToken: 'token_' + Math.random().toString(36),
                connectionId: socket.id
            }
        };
        
        socket.emit('login_response', response);
    });
    
    socket.on('data_request', (data) => {
        console.log('Data request:', data);
        
        const response = {
            messageId: data.messageId,
            sessionId: data.sessionId,
            data: {
                result: 'success',
                payload: { message: 'Data from Node.js server' }
            }
        };
        
        socket.emit('data_response', response);
    });
});

console.log('Node.js server listening on port 3001');
```

### 4. Chạy Spring Boot Application
```bash
# Chạy với Maven
mvn spring-boot:run

# Hoặc build JAR và chạy
mvn clean package -DskipTests
java -jar target/ds-socket-login-1.0.0.jar
```

**Application sẽ khởi động:**
- 🌐 **HTTP Server**: `http://localhost:9095`
- 🔌 **Socket.IO Server**: `http://localhost:9094`
- 📊 **Actuator**: `http://localhost:9095/actuator`

### 5. Test với Web Client
Mở browser và truy cập: `http://localhost:9095/test-client.html`

**Test Client Features:**
- ✅ Real-time Socket.IO connection
- ✅ Login/Authentication testing
- ✅ Data request/response
- ✅ Heartbeat monitoring
- ✅ Auto-reconnection
- ✅ Event logging

## Cấu Hình

### application.yml
```yaml
# Socket.IO Server
socketio:
  server:
    hostname: localhost
    port: 9092
    worker-threads: 100

# Redis Configuration
spring:
  redis:
    host: localhost
    port: 6379
    database: 0

# Node.js Server
nodejs:
  server:
    socket-url: "http://localhost:3001"
    connection-timeout: 5000
    reconnect-attempts: 5
```

## API Endpoints

### Monitoring APIs
- `GET /api/monitoring/health` - System health check
- `GET /api/monitoring/stats` - System statistics
- `GET /api/monitoring/sessions/{sessionId}` - Session details
- `GET /api/monitoring/sessions` - All active sessions
- `GET /api/monitoring/users/{userId}/sessions` - User sessions

### Socket.IO Events

#### Client → Server
- `connect` - Kết nối client
- `login` - Đăng nhập
- `data_request` - Yêu cầu dữ liệu
- `heartbeat` - Heartbeat ping
- `disconnect` - Ngắt kết nối

#### Server → Client
- `connected` - Xác nhận kết nối
- `login_response` - Phản hồi đăng nhập
- `data_response` - Phản hồi dữ liệu
- `heartbeat_ack` - Heartbeat acknowledgment
- `error` - Thông báo lỗi

## Luồng Hoạt Động Chi Tiết

### 1. Client Connection
```
1. Web Client kết nối Socket.IO → Java Server (port 9092)
2. Java tạo session trong Redis
3. Java gửi acknowledgment về client
```

### 2. Authentication Flow
```
1. Client gửi login request → Java
2. Java forward request → Node.js
3. Node.js xử lý authentication
4. Node.js trả access token → Java
5. Java update session với token
6. Java gửi token về client
```

### 3. Data Exchange
```
1. Client gửi data request (với token) → Java
2. Java validate session và token
3. Java forward request → Node.js
4. Node.js xử lý và trả response → Java
5. Java forward response → Client
```

### 4. Cleanup Process
```
1. Client disconnect → Java
2. Java cleanup session trong Redis
3. Java đóng connection với Node.js (nếu cần)
4. Scheduled cleanup cho expired sessions
```

## Performance & Scaling

### Recommended Settings
- **JVM**: `-Xms4g -Xmx8g -XX:+UseG1GC`
- **Redis**: Memory optimization, connection pooling
- **Socket.IO**: Worker threads = CPU cores * 2
- **Session timeout**: 30 minutes default

### Monitoring Metrics
- Active connections count
- Session creation/cleanup rate
- Memory usage
- Redis performance
- Node.js connection health

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   ```bash
   # Check Redis status
   redis-cli ping
   ```

2. **Node.js Connection Failed**
   ```bash
   # Check Node.js server
   curl http://localhost:3001
   ```

3. **Socket.IO Connection Issues**
   - Check CORS configuration
   - Verify port availability
   - Check firewall settings

### Logs
```bash
# Application logs
tail -f logs/socket-login-system.log

# Redis logs
redis-cli monitor
```

## Development

### Project Structure
```
src/
├── main/java/com/example/dssocketlogin/
│   ├── config/          # Configuration classes
│   ├── controller/      # REST controllers
│   ├── handler/         # Socket.IO event handlers
│   ├── model/          # Data models
│   └── service/        # Business logic services
├── main/resources/
│   ├── static/         # Static web files
│   └── application.yml # Configuration
└── test/               # Unit tests
```

### Testing
```bash
# Run unit tests
mvn test

# Run integration tests
mvn verify
```

## License
MIT License
