#!/bin/bash

# DS Socket Login System - Startup Script
echo "🚀 Starting DS Socket Login System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a service is running
check_service() {
    local service_name=$1
    local port=$2
    local url=$3
    
    echo -e "${BLUE}Checking ${service_name}...${NC}"
    
    if curl -s "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ ${service_name} is running on port ${port}${NC}"
        return 0
    else
        echo -e "${RED}❌ ${service_name} is not running on port ${port}${NC}"
        return 1
    fi
}

# Function to wait for service
wait_for_service() {
    local service_name=$1
    local port=$2
    local url=$3
    local max_attempts=30
    local attempt=1
    
    echo -e "${YELLOW}Waiting for ${service_name} to start...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ ${service_name} is ready!${NC}"
            return 0
        fi
        
        echo -e "${YELLOW}Attempt ${attempt}/${max_attempts} - ${service_name} not ready yet...${NC}"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}❌ ${service_name} failed to start after ${max_attempts} attempts${NC}"
    return 1
}

# Step 1: Check Redis
echo -e "\n${BLUE}=== Step 1: Checking Redis ===${NC}"
if ! redis-cli ping > /dev/null 2>&1; then
    echo -e "${RED}❌ Redis is not running. Please start Redis first:${NC}"
    echo -e "${YELLOW}redis-server${NC}"
    echo -e "${YELLOW}# Or with Docker: docker run -d -p 6379:6379 redis:alpine${NC}"
    exit 1
else
    echo -e "${GREEN}✅ Redis is running${NC}"
fi

# Step 2: Start Node.js Server
echo -e "\n${BLUE}=== Step 2: Starting Node.js Authentication Server ===${NC}"
if ! check_service "Node.js Server" "3001" "http://localhost:3001/health"; then
    echo -e "${YELLOW}Starting Node.js server...${NC}"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        echo -e "${RED}❌ package.json not found. Please run from project root directory.${NC}"
        exit 1
    fi
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}Installing Node.js dependencies...${NC}"
        npm install
    fi
    
    # Start Node.js server in background
    nohup npm start > nodejs-server.log 2>&1 &
    NODEJS_PID=$!
    echo $NODEJS_PID > nodejs-server.pid
    
    # Wait for Node.js server to start
    if wait_for_service "Node.js Server" "3001" "http://localhost:3001/health"; then
        echo -e "${GREEN}✅ Node.js server started successfully (PID: ${NODEJS_PID})${NC}"
    else
        echo -e "${RED}❌ Failed to start Node.js server${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Node.js server is already running${NC}"
fi

# Step 3: Build Spring Boot Application
echo -e "\n${BLUE}=== Step 3: Building Spring Boot Application ===${NC}"
echo -e "${YELLOW}Building project...${NC}"
if mvn clean compile -q; then
    echo -e "${GREEN}✅ Build successful${NC}"
else
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

# Step 4: Start Spring Boot Application
echo -e "\n${BLUE}=== Step 4: Starting Spring Boot Application ===${NC}"
if ! check_service "Spring Boot App" "9095" "http://localhost:9095/actuator/health"; then
    echo -e "${YELLOW}Starting Spring Boot application...${NC}"
    
    # Start Spring Boot in background
    nohup mvn spring-boot:run > spring-boot.log 2>&1 &
    SPRING_PID=$!
    echo $SPRING_PID > spring-boot.pid
    
    # Wait for Spring Boot to start
    if wait_for_service "Spring Boot App" "9095" "http://localhost:9095/actuator/health"; then
        echo -e "${GREEN}✅ Spring Boot application started successfully (PID: ${SPRING_PID})${NC}"
    else
        echo -e "${RED}❌ Failed to start Spring Boot application${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Spring Boot application is already running${NC}"
fi

# Step 5: Final Status Check
echo -e "\n${BLUE}=== Step 5: System Status ===${NC}"
check_service "Redis" "6379" "redis://localhost:6379"
check_service "Node.js Server" "3001" "http://localhost:3001/health"
check_service "Spring Boot App" "9095" "http://localhost:9095/actuator/health"
check_service "Socket.IO Server" "9092" "http://localhost:9092/socket.io/"

echo -e "\n${GREEN}🎉 DS Socket Login System is ready!${NC}"
echo -e "\n${BLUE}=== Access URLs ===${NC}"
echo -e "${YELLOW}📱 Test Client:${NC}      http://localhost:9095/test-client.html"
echo -e "${YELLOW}📊 Health Check:${NC}     http://localhost:9095/actuator/health"
echo -e "${YELLOW}📈 Metrics:${NC}          http://localhost:9095/actuator/metrics"
echo -e "${YELLOW}🔍 Monitoring:${NC}       http://localhost:9095/api/monitoring/stats"
echo -e "${YELLOW}🟢 Node.js Health:${NC}   http://localhost:3001/health"
echo -e "${YELLOW}🟢 Node.js Stats:${NC}    http://localhost:3001/stats"

echo -e "\n${BLUE}=== Test Users ===${NC}"
echo -e "${YELLOW}Username: testuser, Password: testpass${NC}"
echo -e "${YELLOW}Username: admin, Password: admin123${NC}"
echo -e "${YELLOW}Username: user1, Password: pass123${NC}"

echo -e "\n${BLUE}=== Logs ===${NC}"
echo -e "${YELLOW}Node.js logs:${NC}     tail -f nodejs-server.log"
echo -e "${YELLOW}Spring Boot logs:${NC} tail -f spring-boot.log"

echo -e "\n${BLUE}=== Stop System ===${NC}"
echo -e "${YELLOW}Run: ./stop-system.sh${NC}"

echo -e "\n${GREEN}✨ Happy testing! ✨${NC}"
